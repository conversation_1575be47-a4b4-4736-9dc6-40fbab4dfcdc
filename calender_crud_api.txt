**************Create calendar API Starts************
Method: post
API URL:
https://gclassroom-839391304260.us-central1.run.app/create_class_calendar

body json: 
{"class_name": "Test Class", "instructor_name": "<EMAIL>"}

heaer:
x-api-key : G$$gle@VedMG!@#

Output:


{
    "message": "Created calendar with ID: <EMAIL>"
}

**************Create calendar API Ends************

**************list calendar API Starts************
Method: post
API URL:
https://gclassroom-839391304260.us-central1.run.app/list_calendar

body json: 
{"instructor_name": "<EMAIL>"}

heaer:
x-api-key : G$$gle@VedMG!@#

Output:


[{"id": "<EMAIL>", "summary": "Test
Class"}]
**************list calendar API Ends************

**************Delete calendar API Starts************
Method: post
API URL:
https://gclassroom-839391304260.us-central1.run.app/delete_calendar

body json: 
{"instructor_name": "<EMAIL>", "calendar_id": "<EMAIL>"}

heaer:
x-api-key : G$$gle@VedMG!@#

Output:


{
    "message": "Calendar '<EMAIL>' deleted successfully."
}
**************Delete calendar API Starts API Ends************