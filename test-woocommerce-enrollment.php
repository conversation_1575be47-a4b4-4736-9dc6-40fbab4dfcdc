<?php
// Standalone CLI test script for VedMG ClassRoom WooCommerce enrollment flow
// Usage:
//   C:\xampp\php\php.exe "c:\xampp\htdocs\paylearn\wp-content\plugins\VedMG-ClassRoom\test-woocommerce-enrollment.php" [ORDER_ID]

// Locate and load WordPress
$wp_load = __DIR__ . '/../../../wp-load.php';
if (!file_exists($wp_load)) {
    echo "ERROR: Unable to find wp-load.php at $wp_load\n";
    exit(1);
}
require_once $wp_load;

// Ensure our plugin core is available
if (!function_exists('vedmg_log_info')) {
    $plugin_main = __DIR__ . '/VedMG-ClassRoom.php';
    if (file_exists($plugin_main)) {
        require_once $plugin_main;
    }
}

// Enable plugin debug logging for this run
if (function_exists('update_option')) {
    update_option('vedmg_classroom_debug_enabled', 1);
}

// Basic guards
if (!class_exists('VedMG_ClassRoom_WooCommerce_Integration')) {
    echo "ERROR: VedMG_ClassRoom_WooCommerce_Integration not available. Is the plugin active?\n";
    exit(1);
}

// Helpers
function cli_log($msg) { echo '[' . date('Y-m-d H:i:s') . "] $msg\n"; }

// Parse CLI args
$order_id = isset($argv[1]) ? intval($argv[1]) : 0;

cli_log('Starting WooCommerce enrollment test');
cli_log('PHP: ' . PHP_VERSION);
cli_log('WP: ' . (function_exists('get_bloginfo') ? get_bloginfo('version') : 'unknown'));
cli_log('WooCommerce active: ' . (class_exists('WooCommerce') ? 'yes' : 'no'));

// Fetch target orders
$orders_to_process = [];
if ($order_id > 0) {
    $orders_to_process = [$order_id];
    cli_log("Using provided order ID: $order_id");
} else if (function_exists('wc_get_orders')) {
    // Get recent completed/processing orders
    $wc_orders = wc_get_orders([
        'limit' => 5,
        'orderby' => 'date',
        'order' => 'DESC',
        'status' => ['completed','processing'],
        'return' => 'ids',
    ]);
    $orders_to_process = array_map('intval', $wc_orders);
    cli_log('Fetched ' . count($orders_to_process) . ' recent orders to process');
} else {
    global $wpdb;
    $results = $wpdb->get_col(
        "SELECT ID FROM {$wpdb->posts} WHERE post_type='shop_order' AND post_status IN ('wc-completed','wc-processing') ORDER BY post_date DESC LIMIT 5"
    );
    $orders_to_process = array_map('intval', $results);
    cli_log('Fetched ' . count($orders_to_process) . ' recent orders to process (raw SQL)');
}

if (empty($orders_to_process)) {
    cli_log('No eligible orders found. Provide an ORDER_ID argument to test a specific order.');
    exit(0);
}

$successes = 0; $failures = 0;
foreach ($orders_to_process as $oid) {
    cli_log("Processing order ID: $oid via handle_order_completed()...");
    try {
        VedMG_ClassRoom_WooCommerce_Integration::handle_order_completed($oid);
        $successes++;
    } catch (Throwable $e) {
        $failures++;
        cli_log('ERROR processing order ' . $oid . ': ' . $e->getMessage());
    }
}

cli_log("Completed. Success: $successes, Failures: $failures");

// Show database table information for debugging
cli_log('--- Database Table Analysis ---');
global $wpdb;

// Check for Vedmg-woo-LMS tables
$vedmg_tables = $wpdb->get_results("SHOW TABLES LIKE '{$wpdb->prefix}%vedmg%'");
$woo_tables = $wpdb->get_results("SHOW TABLES LIKE '{$wpdb->prefix}%woo%'");
cli_log('Found potential Vedmg-woo-LMS tables:');
foreach (array_merge($vedmg_tables, $woo_tables) as $table) {
    $table_name = array_values((array)$table)[0];
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    cli_log("  - $table_name ($count rows)");
}

// Check our courses table
$course_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_courses");
$courses_with_calendar = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_courses WHERE calendar_id IS NOT NULL AND calendar_id != ''");
cli_log("VedMG Courses: $course_count total, $courses_with_calendar with calendar_id");

// Tail last 100 lines of plugin debug.log to surface detection and API activity
$debug_log = __DIR__ . '/debug.log';
if (file_exists($debug_log)) {
    $lines = file($debug_log, FILE_IGNORE_NEW_LINES);
    $tail = array_slice($lines, -100);
    cli_log('--- Last 100 lines of debug.log ---');
    foreach ($tail as $l) echo $l . "\n";
    cli_log('--- End debug.log ---');
} else {
    cli_log('debug.log not found at ' . $debug_log);
}

exit(0);

