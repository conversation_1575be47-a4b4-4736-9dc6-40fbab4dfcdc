**************Share calender API Starts************
Method: post
API URL:
https://gclassroom-************.us-central1.run.app/share_calender

body json: 
{"calendar_id": "<EMAIL>", "user_email": "<EMAIL>", "instructor_email": "<EMAIL>"}

heaer:
x-api-key : G$$gle@VedMG!@#

Output:


{'message': "User added with ACL ID: 121"}

**************Share calender API Ends************

**************Share invite API Starts************
Method: post
API URL:
https://gclassroom-************.us-central1.run.app/share_invite

body json: 
{"calendar_id": "<EMAIL>", "user_email": "<EMAIL>", "summary": "Project Kickoff14", "location": "On Google Meet", "description": "Initial meeting to kickoff the new project.Please join ath http://meet.google.com", "start_datetime": "2025-08-15T22:30:00+05:30", "end_datetime": "2025-08-15T23:00:00+05:30", "instructor_email": "<EMAIL>", "meeting_frequency": "FREQ=WEEKLY;BYDAY=MO,TU,WE;COUNT=3"}

heaer:
x-api-key : G$$gle@VedMG!@#

Output:


{"kind": "calendar#event", "etag": "\"3511415369695774\"", "id": "di4ekf6oln0445ns688ena167g", "status": "confirmed",
"htmlLink":
"https://www.google.com/calendar/event?eid=ZGk0ZWtmNm9sbjA0NDVuczY4OGVuYTE2N2dfMjAyNTA4MTVUMTcwMDAwWiBjX2NsYXNzcm9vbTQ5NWZjMGQzQGc",
"created": "2025-08-20T16:34:44.000Z", "updated": "2025-08-20T16:34:44.847Z", "summary": "Project Kickoff14",
"description": "Initial meeting to kickoff the new project.Please join ath http://meet.google.com", "location": "On
Google Meet", "creator": {"email": "<EMAIL>"}, "organizer": {"email":
"<EMAIL>", "displayName": "Overview of VedMG Academy A", "self": true}, "start":
{"dateTime": "2025-08-15T22:30:00+05:30", "timeZone": "Asia/Kolkata"}, "end": {"dateTime": "2025-08-15T23:00:00+05:30",
"timeZone": "Asia/Kolkata"}, "recurrence": ["RRULE:FREQ=WEEKLY;COUNT=3;BYDAY=MO,TU,WE"], "iCalUID":
"<EMAIL>", "sequence": 0, "attendees": [{"email": "<EMAIL>",
"responseStatus": "needsAction"}], "reminders": {"useDefault": true}, "eventType": "default"}
**************Share invite API Starts API Ends************

**************Get all API Starts************
Method: post
API URL:
https://gclassroom-************.us-central1.run.app/get_all_calender_invites

body json: 
{"calendar_id": "Overview of VedMG Technology1", "instructor_email": "<EMAIL>"}

heaer:
x-api-key : G$$gle@VedMG!@#

Output:


[]
**************Archieve Class API Ends************