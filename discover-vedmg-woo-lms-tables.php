<?php
// Standalone script to discover Vedmg-woo-LMS plugin table structure
// Usage: C:\xampp\php\php.exe "c:\xampp\htdocs\paylearn\wp-content\plugins\VedMG-ClassRoom\discover-vedmg-woo-lms-tables.php"

// Load WordPress
$wp_load = __DIR__ . '/../../../wp-load.php';
if (!file_exists($wp_load)) {
    echo "ERROR: Unable to find wp-load.php at $wp_load\n";
    exit(1);
}
require_once $wp_load;

function cli_log($msg) { echo '[' . date('Y-m-d H:i:s') . "] $msg\n"; }

cli_log('Discovering Vedmg-woo-LMS plugin tables and structure...');

global $wpdb;

// Search for all tables that might be related to Vedmg-woo-LMS
$search_patterns = array(
    '%vedmg%',
    '%woo%lms%',
    '%course%mapping%',
    '%product%course%',
    '%lms%'
);

$found_tables = array();

foreach ($search_patterns as $pattern) {
    $tables = $wpdb->get_results("SHOW TABLES LIKE '$pattern'");
    foreach ($tables as $table) {
        $table_name = array_values((array)$table)[0];
        if (!in_array($table_name, $found_tables)) {
            $found_tables[] = $table_name;
        }
    }
}

cli_log('Found ' . count($found_tables) . ' potentially relevant tables:');

foreach ($found_tables as $table_name) {
    cli_log("\n=== TABLE: $table_name ===");
    
    // Get row count
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    cli_log("Rows: $count");
    
    // Get table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    cli_log("Columns:");
    foreach ($columns as $column) {
        cli_log("  - {$column->Field} ({$column->Type}) " . 
               ($column->Null === 'YES' ? 'NULL' : 'NOT NULL') . 
               ($column->Key ? " KEY:{$column->Key}" : '') .
               ($column->Default !== null ? " DEFAULT:{$column->Default}" : ''));
    }
    
    // Show sample data if table has rows
    if ($count > 0 && $count < 1000) {
        cli_log("Sample data (first 5 rows):");
        $sample_data = $wpdb->get_results("SELECT * FROM $table_name LIMIT 5", ARRAY_A);
        foreach ($sample_data as $row_index => $row) {
            cli_log("  Row " . ($row_index + 1) . ":");
            foreach ($row as $col => $val) {
                $display_val = strlen($val) > 50 ? substr($val, 0, 50) . '...' : $val;
                cli_log("    $col: $display_val");
            }
        }
    }
}

// Specifically look for product-course relationships
cli_log("\n=== SEARCHING FOR PRODUCT-COURSE RELATIONSHIPS ===");

// Check WooCommerce product meta for course relationships
$product_meta = $wpdb->get_results("
    SELECT pm.meta_key, COUNT(*) as count, GROUP_CONCAT(DISTINCT pm.meta_value LIMIT 5) as sample_values
    FROM {$wpdb->postmeta} pm
    JOIN {$wpdb->posts} p ON pm.post_id = p.ID
    WHERE p.post_type = 'product'
    AND (pm.meta_key LIKE '%course%' OR pm.meta_key LIKE '%lms%' OR pm.meta_key LIKE '%stm%')
    GROUP BY pm.meta_key
    ORDER BY count DESC
");

cli_log("WooCommerce product meta keys related to courses:");
foreach ($product_meta as $meta) {
    cli_log("  - {$meta->meta_key}: {$meta->count} products (samples: {$meta->sample_values})");
}

// Check for any tables with both product_id and course_id columns
cli_log("\nTables with both product_id and course_id columns:");
foreach ($found_tables as $table_name) {
    $columns = $wpdb->get_col("DESCRIBE $table_name", 0);
    $has_product = false;
    $has_course = false;
    
    foreach ($columns as $col) {
        if (stripos($col, 'product') !== false) $has_product = true;
        if (stripos($col, 'course') !== false) $has_course = true;
    }
    
    if ($has_product && $has_course) {
        cli_log("  ✅ $table_name - " . implode(', ', $columns));
        
        // Show sample mapping data
        $sample = $wpdb->get_results("SELECT * FROM $table_name LIMIT 3", ARRAY_A);
        foreach ($sample as $i => $row) {
            cli_log("    Sample " . ($i+1) . ": " . json_encode($row));
        }
    }
}

cli_log("\nDiscovery complete!");
