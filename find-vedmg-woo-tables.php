<?php
// Script to find Vedmg-woo-LMS plugin tables and their structure
// Usage: C:\xampp\php\php.exe "c:\xampp\htdocs\paylearn\wp-content\plugins\VedMG-ClassRoom\find-vedmg-woo-tables.php"

// Load WordPress
$wp_load = __DIR__ . '/../../../wp-load.php';
if (!file_exists($wp_load)) {
    echo "ERROR: Unable to find wp-load.php at $wp_load\n";
    exit(1);
}
require_once $wp_load;

function cli_log($msg) { echo '[' . date('Y-m-d H:i:s') . "] $msg\n"; }

cli_log('Searching for Vedmg-woo-LMS plugin tables...');

global $wpdb;

// Search for tables that might contain product-to-course mappings
$search_patterns = array(
    '%vedmg%',
    '%woo%lms%',
    '%course%mapping%',
    '%product%course%',
    '%lms%'
);

$found_tables = array();

foreach ($search_patterns as $pattern) {
    $tables = $wpdb->get_results($wpdb->prepare("SHOW TABLES LIKE %s", $pattern));
    foreach ($tables as $table) {
        $table_name = array_values((array)$table)[0];
        if (!in_array($table_name, $found_tables)) {
            $found_tables[] = $table_name;
        }
    }
}

cli_log('Found ' . count($found_tables) . ' potential tables:');

foreach ($found_tables as $table_name) {
    cli_log("\n=== Table: $table_name ===");
    
    // Get row count
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    cli_log("Rows: $count");
    
    // Get table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    cli_log("Columns:");
    foreach ($columns as $column) {
        cli_log("  - {$column->Field} ({$column->Type}) " . ($column->Null == 'YES' ? 'NULL' : 'NOT NULL'));
    }
    
    // Show sample data if table has reasonable size
    if ($count > 0 && $count < 1000) {
        cli_log("Sample data (first 5 rows):");
        $sample_data = $wpdb->get_results("SELECT * FROM $table_name LIMIT 5", ARRAY_A);
        foreach ($sample_data as $row) {
            $row_str = array();
            foreach ($row as $key => $value) {
                $row_str[] = "$key: " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value);
            }
            cli_log("  " . implode(' | ', $row_str));
        }
    }
}

// Also check for any active plugins that might be the Vedmg-woo-LMS
cli_log("\n=== Active Plugins ===");
$active_plugins = get_option('active_plugins', array());
foreach ($active_plugins as $plugin) {
    if (stripos($plugin, 'vedmg') !== false || stripos($plugin, 'woo') !== false || stripos($plugin, 'lms') !== false) {
        cli_log("Relevant plugin: $plugin");
    }
}

cli_log("\nSearch complete.");
