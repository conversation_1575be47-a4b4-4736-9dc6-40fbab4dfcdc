<?php
/**
 * Delete All Kushagra's Classrooms Script
 * 
 * This script will:
 * 1. Get all <NAME_EMAIL> using list_courses API
 * 2. Loop through each classroom and archive it first
 * 3. Then delete each archived classroom
 * 4. Provide detailed logging of the entire process
 * 
 * Run with: C:\xampp\php\php.exe "c:\xampp\htdocs\paylearn\wp-content\plugins\VedMG-ClassRoom\delete_all_kushagra_classrooms.php"
 */

// Load WordPress
$wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
if (!file_exists($wp_load_path)) {
    die("❌ WordPress not found. Please check the path.\n");
}

require_once $wp_load_path;

echo "🗑️  DELETE ALL KUSHAGRA'S CLASSROOMS SCRIPT\n";
echo "===========================================\n\n";

// API Configuration (from list_api.txt and create_delete_course_api.txt)
$api_base_url = 'https://gclassroom-************.us-central1.run.app';
$api_key = 'G$$gle@VedMG!@#';
$instructor_email = '<EMAIL>';

// Global counters
$total_classrooms = 0;
$archived_count = 0;
$deleted_count = 0;
$failed_count = 0;

/**
 * Step 1: Get all classrooms under kushagra's name
 */
function get_all_kushagra_classrooms() {
    global $api_base_url, $api_key, $instructor_email, $total_classrooms;
    
    echo "1️⃣ GETTING ALL CLASSROOMS FOR KUSHAGRA\n";
    echo "======================================\n";
    
    $api_url = $api_base_url . '/list_courses';
    
    echo "📡 Making API call to list courses...\n";
    echo "   API URL: $api_url\n";
    echo "   Instructor Email: $instructor_email\n";
    echo "   API Key: " . substr($api_key, 0, 5) . "***\n\n";
    
    // Prepare API request
    $post_data = json_encode([
        'instructor_email' => $instructor_email
    ]);
    
    $response = wp_remote_post($api_url, array(
        'timeout' => 30,
        'headers' => array(
            'Content-Type' => 'application/json',
            'x-api-key' => $api_key
        ),
        'body' => $post_data
    ));
    
    if (is_wp_error($response)) {
        echo "❌ API call failed: " . $response->get_error_message() . "\n";
        return false;
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    echo "📊 API Response:\n";
    echo "   Status Code: $response_code\n";
    echo "   Response Size: " . strlen($response_body) . " bytes\n\n";
    
    if ($response_code !== 200) {
        echo "❌ API returned error code: $response_code\n";
        echo "   Response: $response_body\n";
        return false;
    }
    
    $classrooms = json_decode($response_body, true);
    
    if (!is_array($classrooms)) {
        echo "❌ Invalid response format\n";
        echo "   Response: $response_body\n";
        return false;
    }
    
    $total_classrooms = count($classrooms);
    
    echo "✅ Successfully retrieved classrooms!\n";
    echo "   Total Classrooms Found: $total_classrooms\n\n";
    
    if ($total_classrooms === 0) {
        echo "ℹ️  No classrooms <NAME_EMAIL>\n";
        return array();
    }
    
    echo "📋 CLASSROOM LIST:\n";
    foreach ($classrooms as $index => $classroom) {
        $classroom_id = $classroom['id'] ?? 'Unknown';
        $classroom_name = $classroom['name'] ?? 'Unknown';
        $course_state = $classroom['courseState'] ?? 'Unknown';
        $creation_time = $classroom['creationTime'] ?? 'Unknown';
        
        echo "   " . ($index + 1) . ". ID: $classroom_id\n";
        echo "      Name: $classroom_name\n";
        echo "      State: $course_state\n";
        echo "      Created: $creation_time\n";
        echo "      Link: " . ($classroom['alternateLink'] ?? 'N/A') . "\n\n";
    }
    
    return $classrooms;
}

/**
 * Step 2: Archive a single classroom
 */
function archive_classroom($classroom_id, $classroom_name) {
    global $api_base_url, $api_key, $instructor_email;
    
    echo "📦 ARCHIVING CLASSROOM: $classroom_id\n";
    echo "   Name: $classroom_name\n";
    
    $api_url = $api_base_url . '/archieve_course'; // Note: API has typo "archieve"
    
    $post_data = json_encode([
        'course_id' => $classroom_id,
        'instructor_email' => $instructor_email
    ]);
    
    echo "   Making archive API call...\n";
    
    $response = wp_remote_post($api_url, array(
        'timeout' => 30,
        'headers' => array(
            'Content-Type' => 'application/json',
            'x-api-key' => $api_key
        ),
        'body' => $post_data
    ));
    
    if (is_wp_error($response)) {
        echo "   ❌ Archive failed: " . $response->get_error_message() . "\n\n";
        return false;
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    if ($response_code === 200) {
        $response_data = json_decode($response_body, true);
        $message = $response_data['message'] ?? 'Archived successfully';
        echo "   ✅ Archive successful: $message\n\n";
        return true;
    } else {
        echo "   ❌ Archive failed (Code: $response_code)\n";
        echo "   Response: $response_body\n\n";
        return false;
    }
}

/**
 * Step 3: Delete a single classroom
 */
function delete_classroom($classroom_id, $classroom_name) {
    global $api_base_url, $api_key, $instructor_email;
    
    echo "🗑️  DELETING CLASSROOM: $classroom_id\n";
    echo "   Name: $classroom_name\n";
    
    $api_url = $api_base_url . '/delete_course';
    
    $post_data = json_encode([
        'course_id' => $classroom_id,
        'instructor_email' => $instructor_email
    ]);
    
    echo "   Making delete API call...\n";
    
    $response = wp_remote_post($api_url, array(
        'timeout' => 30,
        'headers' => array(
            'Content-Type' => 'application/json',
            'x-api-key' => $api_key
        ),
        'body' => $post_data
    ));
    
    if (is_wp_error($response)) {
        echo "   ❌ Delete failed: " . $response->get_error_message() . "\n\n";
        return false;
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);
    
    if ($response_code === 200) {
        $response_data = json_decode($response_body, true);
        $message = $response_data['message'] ?? 'Deleted successfully';
        echo "   ✅ Delete successful: $message\n\n";
        return true;
    } else {
        echo "   ❌ Delete failed (Code: $response_code)\n";
        echo "   Response: $response_body\n\n";
        return false;
    }
}

/**
 * Step 4: Process all classrooms (archive then delete)
 */
function process_all_classrooms($classrooms) {
    global $archived_count, $deleted_count, $failed_count;
    
    echo "2️⃣ PROCESSING ALL CLASSROOMS (ARCHIVE → DELETE)\n";
    echo "===============================================\n";
    
    if (empty($classrooms)) {
        echo "ℹ️  No classrooms to process\n\n";
        return;
    }
    
    $total = count($classrooms);
    echo "📊 Starting bulk deletion process for $total classrooms...\n\n";
    
    foreach ($classrooms as $index => $classroom) {
        $classroom_id = $classroom['id'] ?? '';
        $classroom_name = $classroom['name'] ?? 'Unknown';
        $course_state = $classroom['courseState'] ?? 'Unknown';
        
        if (empty($classroom_id)) {
            echo "⚠️  Skipping classroom with missing ID\n\n";
            $failed_count++;
            continue;
        }
        
        echo "🔄 PROCESSING CLASSROOM " . ($index + 1) . "/$total\n";
        echo "=" . str_repeat("=", 40) . "\n";
        echo "   ID: $classroom_id\n";
        echo "   Name: $classroom_name\n";
        echo "   Current State: $course_state\n\n";
        
        // Step 1: Archive the classroom
        $archive_success = archive_classroom($classroom_id, $classroom_name);
        
        if ($archive_success) {
            $archived_count++;
            
            // Wait a moment between archive and delete
            echo "   ⏳ Waiting 2 seconds before deletion...\n";
            sleep(2);
            
            // Step 2: Delete the archived classroom
            $delete_success = delete_classroom($classroom_id, $classroom_name);
            
            if ($delete_success) {
                $deleted_count++;
                echo "   🎉 Classroom completely removed!\n";
            } else {
                $failed_count++;
                echo "   ⚠️  Classroom archived but deletion failed\n";
            }
        } else {
            $failed_count++;
            echo "   ❌ Archive failed - skipping deletion\n";
        }
        
        echo "   " . str_repeat("-", 50) . "\n\n";
        
        // Small delay between classrooms to avoid overwhelming the API
        if ($index < $total - 1) {
            echo "   ⏳ Waiting 1 second before next classroom...\n\n";
            sleep(1);
        }
    }
}

/**
 * Step 5: Verify cleanup by checking remaining classrooms
 */
function verify_cleanup() {
    echo "3️⃣ VERIFYING CLEANUP\n";
    echo "====================\n";
    
    echo "📡 Checking remaining classrooms...\n";
    
    $remaining_classrooms = get_all_kushagra_classrooms();
    
    if ($remaining_classrooms === false) {
        echo "⚠️  Could not verify cleanup - API call failed\n\n";
        return false;
    }
    
    $remaining_count = count($remaining_classrooms);
    
    if ($remaining_count === 0) {
        echo "✅ CLEANUP VERIFICATION SUCCESSFUL!\n";
        echo "   No classrooms <NAME_EMAIL>\n";
        echo "   All classrooms have been completely removed\n\n";
        return true;
    } else {
        echo "⚠️  CLEANUP INCOMPLETE\n";
        echo "   $remaining_count classrooms still exist:\n\n";
        
        foreach ($remaining_classrooms as $classroom) {
            $classroom_id = $classroom['id'] ?? 'Unknown';
            $classroom_name = $classroom['name'] ?? 'Unknown';
            $course_state = $classroom['courseState'] ?? 'Unknown';
            
            echo "   • ID: $classroom_id\n";
            echo "     Name: $classroom_name\n";
            echo "     State: $course_state\n\n";
        }
        
        return false;
    }
}

/**
 * Generate final summary report
 */
function generate_summary_report() {
    global $total_classrooms, $archived_count, $deleted_count, $failed_count;
    
    echo "📊 BULK DELETION SUMMARY REPORT\n";
    echo "===============================\n\n";
    
    echo "🎯 DELETION STATISTICS:\n";
    echo "• Total Classrooms Found: $total_classrooms\n";
    echo "• Successfully Archived: $archived_count\n";
    echo "• Successfully Deleted: $deleted_count\n";
    echo "• Failed Operations: $failed_count\n\n";
    
    $success_rate = $total_classrooms > 0 ? ($deleted_count / $total_classrooms) * 100 : 0;
    
    echo "🎯 OVERALL SUCCESS RATE: " . number_format($success_rate, 1) . "% ($deleted_count/$total_classrooms)\n\n";
    
    if ($success_rate === 100) {
        echo "🎉 PERFECT! ALL CLASSROOMS DELETED SUCCESSFULLY!\n";
        echo "✅ Complete cleanup achieved\n";
        echo "✅ No manual intervention required\n";
        echo "✅ <EMAIL> account is clean\n\n";
    } elseif ($success_rate >= 80) {
        echo "⚠️  MOSTLY SUCCESSFUL - Minor issues detected\n";
        echo "🔧 Some classrooms may need manual attention\n";
        echo "📋 Check failed operations above\n\n";
    } else {
        echo "❌ SIGNIFICANT ISSUES - Many deletions failed\n";
        echo "🔧 Manual cleanup may be required\n";
        echo "📋 Check API connectivity and permissions\n\n";
    }
    
    echo "📝 PROCESS DETAILS:\n";
    echo "• Instructor Email: <EMAIL>\n";
    echo "• API Endpoint: https://gclassroom-************.us-central1.run.app\n";
    echo "• Process: List → Archive → Delete → Verify\n";
    echo "• Deletion Method: Archive first, then delete (Google requirement)\n\n";
    
    if ($failed_count > 0) {
        echo "🔧 TROUBLESHOOTING FAILED OPERATIONS:\n";
        echo "• Check API key validity\n";
        echo "• Verify instructor permissions\n";
        echo "• Check classroom states (some may not be deletable)\n";
        echo "• Review API rate limits\n";
        echo "• Manual deletion may be needed for remaining classrooms\n\n";
    }
    
    echo "🎯 BULK DELETION PROCESS COMPLETE!\n";
}

// Run the complete bulk deletion process
echo "🚀 Starting Bulk Deletion of All Kushagra's Classrooms...\n\n";

echo "⚠️  WARNING: This will delete ALL <NAME_EMAIL>\n";
echo "📋 Process: List Classrooms → Archive Each → Delete Each → Verify Cleanup\n\n";

// Step 1: Get all classrooms
$classrooms = get_all_kushagra_classrooms();

if ($classrooms === false) {
    echo "❌ Failed to retrieve classrooms - aborting deletion process\n";
    exit(1);
}

if (empty($classrooms)) {
    echo "ℹ️  No classrooms found - nothing to delete\n";
    echo "✅ <EMAIL> account is already clean\n";
    exit(0);
}

// Confirmation prompt (commented out for automated testing)
// echo "⚠️  Found $total_classrooms classrooms. Continue with deletion? (y/N): ";
// $confirmation = trim(fgets(STDIN));
// if (strtolower($confirmation) !== 'y') {
//     echo "❌ Deletion cancelled by user\n";
//     exit(0);
// }

echo "🔥 PROCEEDING WITH BULK DELETION...\n\n";

// Step 2: Process all classrooms (archive then delete)
process_all_classrooms($classrooms);

// Step 3: Verify cleanup
$cleanup_verified = verify_cleanup();

// Step 4: Generate summary report
generate_summary_report();

echo "\n🗑️  BULK DELETION SCRIPT COMPLETE!\n\n";

if ($deleted_count === $total_classrooms && $cleanup_verified) {
    echo "🎉 SUCCESS! All kushagra's classrooms have been deleted!\n";
    echo "✅ Complete cleanup achieved\n";
    echo "✅ Account is ready for fresh testing\n";
} elseif ($deleted_count > 0) {
    echo "⚠️  Partial success - some classrooms deleted\n";
    echo "🔧 Manual cleanup may be needed for remaining classrooms\n";
} else {
    echo "❌ No classrooms were deleted\n";
    echo "🔧 Check API configuration and try again\n";
}

echo "\n📝 Script execution completed!\n";
?>
