<?php
/**
 * VedMG ClassRoom Database Deactivator
 * 
 * This file handles all database cleanup when the plugin is deactivated.
 * It removes only the 3 essential database tables and plugin-related data.
 * 
 * CAUTION: This will permanently delete all data!
 * 
 * Tables Removed (3 Essential Tables Only):
 * 1. vedmg_courses - Course details
 * 2. vedmg_student_enrollments - Student enrollments
 * 3. vedmg_class_sessions - Class sessions
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom Database Deactivator Class
 * 
 * Handles all database cleanup and data removal
 */
class VedMG_ClassRoom_Database_Deactivator {
    
    /**
     * Deactivate the database
     * Removes all plugin-related tables and data
     * 
     * @param bool $delete_data Whether to delete all data (default: true)
     */
    public static function deactivate($delete_data = true) {
        // Log deactivation start
        vedmg_log_info('DATABASE', 'Starting database deactivation');
        
        try {
            if ($delete_data) {
                // Backup data before deletion (optional)
                self::backup_data_before_deletion();
                
                // Drop all database tables
                self::drop_all_tables();
                
                // Remove plugin options
                self::remove_plugin_options();
                
                // Clear any cached data
                self::clear_cached_data();
                
                // Log successful deactivation
                vedmg_log_info('DATABASE', 'Database deactivation completed - all data removed');
            } else {
                // Just mark as deactivated without deleting data
                self::mark_as_deactivated();
                vedmg_log_info('DATABASE', 'Database marked as deactivated - data preserved');
            }
            
        } catch (Exception $e) {
            // Log deactivation error
            vedmg_log_error('DATABASE', 'Database deactivation failed', $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Drop all plugin tables
     * Removes only the 3 essential database tables created by the plugin
     */
    private static function drop_all_tables() {
        global $wpdb;
        
        // List of tables to drop (5 tables total, in reverse dependency order)
        $tables_to_drop = array(
            'vedmg_student_classroom_mappings',  // Drop mappings first (depends on enrollments)
            'vedmg_class_sessions',              // Drop class sessions (depends on courses)
            'vedmg_student_enrollments',         // Then student enrollments (depends on courses)
            'vedmg_instructor_sync',             // Drop instructor sync (independent)
            'vedmg_courses'                      // Finally courses (main table)
        );
        
        foreach ($tables_to_drop as $table) {
            $table_name = $wpdb->prefix . $table;
            
            // Check if table exists before dropping
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
            
            if ($table_exists === $table_name) {
                // Drop the table
                $wpdb->query("DROP TABLE IF EXISTS $table_name");
                
                // Verify table was dropped
                $still_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
                
                if (!$still_exists) {
                    vedmg_log_database('DROP', $table, 'Table dropped successfully');
                } else {
                    vedmg_log_error('DATABASE', "Failed to drop table", "Table $table_name still exists");
                }
            } else {
                vedmg_log_info('DATABASE', "Table $table_name does not exist, skipping");
            }
        }
    }
    
    /**
     * Remove plugin options
     * Deletes all WordPress options created by the plugin
     */
    private static function remove_plugin_options() {
        // List of plugin options to remove
        $options_to_remove = array(
            'vedmg_classroom_debug_enabled',
            'vedmg_classroom_version',
            'vedmg_classroom_db_version',
            'vedmg_classroom_db_created_date',
            'vedmg_classroom_google_api_key',
            'vedmg_classroom_google_credentials',
            'vedmg_classroom_settings',
            'vedmg_classroom_last_sync',
            'vedmg_classroom_api_rate_limit',
            'vedmg_classroom_error_notifications'
        );
        
        foreach ($options_to_remove as $option) {
            if (get_option($option) !== false) {
                delete_option($option);
                vedmg_log_database('DELETE', 'wp_options', "Option $option removed");
            }
        }
        
        // Remove any options with plugin prefix
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE 'vedmg_classroom_%'");
        
        vedmg_log_info('DATABASE', 'All plugin options removed');
    }
    
    /**
     * Clear cached data
     * Removes any WordPress cache or transient data
     */
    private static function clear_cached_data() {
        // Remove transients
        global $wpdb;
        
        // Delete transients with our plugin prefix
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_vedmg_classroom_%' OR option_name LIKE '_transient_timeout_vedmg_classroom_%'");
        
        // Clear object cache if available
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Clear any custom cache directories (if we create any in future)
        $cache_dir = WP_CONTENT_DIR . '/cache/vedmg-classroom/';
        if (is_dir($cache_dir)) {
            self::remove_directory_recursive($cache_dir);
        }
        
        vedmg_log_info('DATABASE', 'Cached data cleared');
    }
    
    /**
     * Backup data before deletion
     * Creates a backup of important data before removing everything
     */
    private static function backup_data_before_deletion() {
        global $wpdb;
        
        // Only create backup if there's actual data
        $courses_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_courses");
        $enrollments_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_student_enrollments");
        
        if ($courses_count > 0 || $enrollments_count > 0) {
            // Create backup directory
            $backup_dir = WP_CONTENT_DIR . '/vedmg-classroom-backups/';
            if (!is_dir($backup_dir)) {
                wp_mkdir_p($backup_dir);
            }
            
            $backup_file = $backup_dir . 'vedmg-classroom-backup-' . date('Y-m-d-H-i-s') . '.sql';
            
            // Create simple backup (you could enhance this with mysqldump)
            $backup_content = "-- VedMG ClassRoom Plugin Data Backup\n";
            $backup_content .= "-- Created: " . current_time('mysql') . "\n\n";
            
            // Add table creation and data for essential tables only
            $tables = array('vedmg_courses', 'vedmg_student_enrollments', 'vedmg_class_sessions');
            
            foreach ($tables as $table) {
                $table_name = $wpdb->prefix . $table;
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
                
                if ($table_exists) {
                    // Get CREATE TABLE statement
                    $create_table = $wpdb->get_row("SHOW CREATE TABLE $table_name", ARRAY_N);
                    if ($create_table) {
                        $backup_content .= $create_table[1] . ";\n\n";
                    }
                    
                    // Get data
                    $rows = $wpdb->get_results("SELECT * FROM $table_name", ARRAY_A);
                    if ($rows) {
                        foreach ($rows as $row) {
                            $values = array();
                            foreach ($row as $value) {
                                $values[] = "'" . esc_sql($value) . "'";
                            }
                            $backup_content .= "INSERT INTO $table_name VALUES (" . implode(', ', $values) . ");\n";
                        }
                        $backup_content .= "\n";
                    }
                }
            }
            
            // Write backup file
            file_put_contents($backup_file, $backup_content);
            
            vedmg_log_info('DATABASE', "Data backup created: $backup_file");
        } else {
            vedmg_log_info('DATABASE', 'No data to backup - tables are empty');
        }
    }
    
    /**
     * Mark as deactivated (preserve data)
     * Updates plugin status without deleting data
     */
    private static function mark_as_deactivated() {
        // Set deactivation flag
        update_option('vedmg_classroom_deactivated', current_time('mysql'));
        update_option('vedmg_classroom_status', 'deactivated');
        
        vedmg_log_info('DATABASE', 'Plugin marked as deactivated - data preserved');
    }
    
    /**
     * Remove directory recursively
     * Helper function to remove directories and all contents
     * 
     * @param string $dir Directory path to remove
     */
    private static function remove_directory_recursive($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), array('.', '..'));
        
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                self::remove_directory_recursive($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
    
    /**
     * Verify complete removal
     * Checks that all plugin data has been removed
     * 
     * @return bool True if all data removed, false otherwise
     */
    public static function verify_complete_removal() {
        global $wpdb;
        
        // Check if any tables still exist (5 tables total)
        $remaining_tables = array();

        $tables_to_check = array(
            'vedmg_courses',
            'vedmg_student_enrollments',
            'vedmg_class_sessions',
            'vedmg_instructor_sync',
            'vedmg_student_classroom_mappings'
        );
        
        foreach ($tables_to_check as $table) {
            $table_name = $wpdb->prefix . $table;
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
            
            if ($table_exists === $table_name) {
                $remaining_tables[] = $table;
            }
        }
        
        // Check if any options still exist
        $remaining_options = $wpdb->get_results(
            "SELECT option_name FROM {$wpdb->options} WHERE option_name LIKE 'vedmg_classroom_%'",
            ARRAY_A
        );
        
        $is_clean = empty($remaining_tables) && empty($remaining_options);
        
        if ($is_clean) {
            vedmg_log_info('DATABASE', 'Complete removal verified - no plugin data remains');
        } else {
            $error_details = array();
            if (!empty($remaining_tables)) {
                $error_details[] = 'Remaining tables: ' . implode(', ', $remaining_tables);
            }
            if (!empty($remaining_options)) {
                $option_names = array_column($remaining_options, 'option_name');
                $error_details[] = 'Remaining options: ' . implode(', ', $option_names);
            }
            
            vedmg_log_error('DATABASE', 'Incomplete removal detected', implode(' | ', $error_details));
        }
        
        return $is_clean;
    }
    
    /**
     * Get removal statistics
     * Returns information about what was removed
     * 
     * @return array Removal statistics
     */
    public static function get_removal_stats() {
        global $wpdb;
        
        $stats = array(
            'tables_removed' => 0,
            'options_removed' => 0,
            'cache_cleared' => true,
            'backup_created' => false,
            'removal_date' => current_time('mysql')
        );
        
        // Count removed tables (5 tables total)
        $tables = array(
            'vedmg_courses',
            'vedmg_student_enrollments',
            'vedmg_class_sessions',
            'vedmg_instructor_sync',
            'vedmg_student_classroom_mappings'
        );
        
        foreach ($tables as $table) {
            $table_name = $wpdb->prefix . $table;
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
            
            if (!$table_exists) {
                $stats['tables_removed']++;
            }
        }
        
        // Check for backup file
        $backup_dir = WP_CONTENT_DIR . '/vedmg-classroom-backups/';
        if (is_dir($backup_dir)) {
            $backup_files = glob($backup_dir . 'vedmg-classroom-backup-*.sql');
            $stats['backup_created'] = !empty($backup_files);
        }
        
        return $stats;
    }
}

?>
