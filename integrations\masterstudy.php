<?php
/**
 * VedMG ClassRoom MasterStudy LMS Integration
 * 
 * This file handles integration with MasterStudy LMS for automatic
 * course data synchronization when instructors create courses.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom MasterStudy Integration Class
 * 
 * Handles integration with MasterStudy LMS for course synchronization
 */
class VedMG_ClassRoom_MasterStudy_Integration {
    
    /**
     * Initialize MasterStudy integration
     */
    public static function init() {
        // Hook into MasterStudy course creation
        add_action('stm_lms_course_created', array(__CLASS__, 'handle_course_created'), 10, 1);
        add_action('stm_lms_course_updated', array(__CLASS__, 'handle_course_updated'), 10, 1);
        add_action('stm_lms_course_published', array(__CLASS__, 'handle_course_published'), 10, 1);

        // Hook into WordPress post save for courses (main automation hook)
        add_action('save_post', array(__CLASS__, 'handle_course_save'), 10, 3);

        // Hook into course deletion for automatic cleanup
        add_action('delete_post', array(__CLASS__, 'on_course_deleted_auto'), 10, 2);

        // Use only transition_post_status to avoid duplicates
        add_action('transition_post_status', array(__CLASS__, 'on_course_status_transition'), 10, 3);

        vedmg_log_info('MASTERSTUDY', 'MasterStudy LMS integration initialized with automation');
    }
    
    /**
     * Handle course created event
     */
    public static function handle_course_created($course_id) {
        vedmg_log_info('MASTERSTUDY', 'Course created event: ' . $course_id);
        
        try {
            self::sync_course_to_vedmg($course_id, 'created');
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Failed to sync created course', $e->getMessage());
        }
    }
    
    /**
     * Handle course updated event
     */
    public static function handle_course_updated($course_id) {
        vedmg_log_info('MASTERSTUDY', 'Course updated event: ' . $course_id);
        
        try {
            self::sync_course_to_vedmg($course_id, 'updated');
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Failed to sync updated course', $e->getMessage());
        }
    }
    
    /**
     * Handle course published event
     */
    public static function handle_course_published($course_id) {
        vedmg_log_info('MASTERSTUDY', 'Course published event: ' . $course_id);
        
        try {
            self::sync_course_to_vedmg($course_id, 'published');
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Failed to sync published course', $e->getMessage());
        }
    }
    
    /**
     * Handle course save via WordPress hook
     */
    public static function handle_course_save($post_id, $post, $update) {
        // Check if this is a MasterStudy course
        if ($post->post_type !== 'stm-courses') {
            return;
        }
        
        // Skip if this is an auto-save or revision
        if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
            return;
        }
        
        // Skip if this is a publish event (handled by transition_post_status)
        if ($post->post_status === 'publish' && !$update) {
            vedmg_log_info('MASTERSTUDY', 'Course save event: ' . $post_id . ' - skipping (handled by publish hook)');
            return;
        }

        vedmg_log_info('MASTERSTUDY', 'Course save event: ' . $post_id . ' (update: ' . ($update ? 'yes' : 'no') . ')');

        try {
            self::sync_course_to_vedmg($post_id, $update ? 'updated' : 'created');
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Failed to sync course on save', $e->getMessage());
        }
    }
    
    /**
     * Sync course data to VedMG database
     */
    private static function sync_course_to_vedmg($masterstudy_course_id, $action) {
        global $wpdb;
        
        // Get course data from MasterStudy
        $course_data = self::get_masterstudy_course_data($masterstudy_course_id);
        if (!$course_data) {
            throw new Exception('Course data not found: ' . $masterstudy_course_id);
        }
        
        // Check if course already exists in VedMG database
        $existing_course = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}vedmg_courses
            WHERE masterstudy_course_id = %d
        ", $masterstudy_course_id));

        // Prepare course data for VedMG database
        $vedmg_course_data = array(
            'masterstudy_course_id' => $masterstudy_course_id,
            'course_name' => $course_data['name'],
            'course_description' => $course_data['description'],
            'instructor_id' => $course_data['instructor_id'],
            'instructor_name' => $course_data['instructor_name'],
            'instructor_email' => $course_data['instructor_email'], // Add instructor email
            'classroom_status' => 'pending', // Default status
            'auto_enroll_enabled' => 1,
            'updated_date' => current_time('mysql')
        );
        
        if ($existing_course) {
            // Update existing course
            $wpdb->update(
                $wpdb->prefix . 'vedmg_courses',
                $vedmg_course_data,
                array('course_id' => $existing_course->course_id),
                array('%d', '%s', '%s', '%d', '%s', '%s', '%s', '%d', '%s'),
                array('%d')
            );

            vedmg_log_info('MASTERSTUDY', 'Updated course in VedMG database: ' . $masterstudy_course_id);
            return $existing_course->course_id;
        } else {
            // Insert new course
            $vedmg_course_data['created_date'] = current_time('mysql');

            $wpdb->insert(
                $wpdb->prefix . 'vedmg_courses',
                $vedmg_course_data,
                array('%d', '%s', '%s', '%d', '%s', '%s', '%s', '%d', '%s', '%s')
            );
            
            $new_course_id = $wpdb->insert_id;
            
            if ($new_course_id) {
                vedmg_log_info('MASTERSTUDY', 'Created new course in VedMG database: ' . $masterstudy_course_id . ' (VedMG ID: ' . $new_course_id . ')');

                // Trigger classroom creation if auto-creation is enabled
                if (get_option('vedmg_auto_create_classroom', true)) {
                    self::trigger_classroom_creation($new_course_id);
                }

                return $new_course_id;
            } else {
                throw new Exception('Failed to insert course into database - no insert ID returned');
            }
        }

        return true;
    }
    
    /**
     * Get course data from MasterStudy
     */
    private static function get_masterstudy_course_data($course_id) {
        $post = get_post($course_id);
        if (!$post || $post->post_type !== 'stm-courses') {
            return false;
        }
        
        // Get instructor information
        $instructor_id = get_post_meta($course_id, 'course_instructor', true);
        $instructor_name = '';
        $instructor_email = '';

        // If no specific instructor is set, use the post author
        if (!$instructor_id) {
            $instructor_id = $post->post_author;
        }

        if ($instructor_id) {
            $instructor = get_userdata($instructor_id);
            if ($instructor) {
                $instructor_name = $instructor->display_name;
                $instructor_email = $instructor->user_email;
            }
        }

        return array(
            'name' => $post->post_title,
            'description' => $post->post_content,
            'instructor_id' => $instructor_id ?: 0,
            'instructor_name' => $instructor_name,
            'instructor_email' => $instructor_email,
            'status' => $post->post_status,
            'created_date' => $post->post_date,
            'modified_date' => $post->post_modified
        );
    }
    
    /**
     * Trigger classroom creation for new course
     */
    private static function trigger_classroom_creation($vedmg_course_id) {
        // This would trigger API call to create Google Classroom
        // For now, just log the action
        vedmg_log_info('CLASSROOM', 'Triggered classroom creation for course: ' . $vedmg_course_id);
        
        // In the future, this would:
        // 1. Call Google Classroom API to create classroom
        // 2. Update the course record with Google Classroom ID
        // 3. Set classroom_status to 'created'
    }
    
    /**
     * Manual sync function for admin use
     */
    public static function manual_sync_all_courses() {
        global $wpdb;
        
        // Get all MasterStudy courses
        $courses = get_posts(array(
            'post_type' => 'stm-courses',
            'post_status' => array('publish', 'draft'),
            'numberposts' => -1
        ));
        
        $synced_count = 0;
        $errors = array();
        
        foreach ($courses as $course) {
            try {
                self::sync_course_to_vedmg($course->ID, 'manual_sync');
                $synced_count++;
            } catch (Exception $e) {
                $errors[] = 'Course ' . $course->ID . ': ' . $e->getMessage();
            }
        }
        
        vedmg_log_info('MASTERSTUDY', 'Manual sync completed. Synced: ' . $synced_count . ', Errors: ' . count($errors));
        
        return array(
            'synced' => $synced_count,
            'errors' => $errors,
            'total' => count($courses)
        );
    }
    
    /**
     * Get course statistics
     */
    public static function get_course_statistics() {
        global $wpdb;
        
        // Count MasterStudy courses
        $masterstudy_courses = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->posts} 
            WHERE post_type = 'stm-courses' AND post_status IN ('publish', 'draft')
        ");
        
        // Count VedMG courses
        $vedmg_courses = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_courses
        ");
        
        // Count courses with classrooms
        $courses_with_classrooms = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_courses 
            WHERE google_classroom_id IS NOT NULL AND google_classroom_id != ''
        ");
        
        return array(
            'masterstudy_courses' => intval($masterstudy_courses),
            'vedmg_courses' => intval($vedmg_courses),
            'courses_with_classrooms' => intval($courses_with_classrooms),
            'sync_needed' => intval($masterstudy_courses) - intval($vedmg_courses)
        );
    }
    
    /**
     * Check if MasterStudy LMS is active
     */
    public static function is_masterstudy_active() {
        return class_exists('MasterStudy\Lms\Plugin') || function_exists('stm_lms_templates');
    }
    
    /**
     * Get MasterStudy version
     */
    public static function get_masterstudy_version() {
        if (defined('STM_LMS_VERSION')) {
            return STM_LMS_VERSION;
        }
        return 'Unknown';
    }
    
    /**
     * Manual sync all MasterStudy courses
     * Called when sync button is clicked
     */
    public static function manual_sync_all() {
        global $wpdb;
        
        vedmg_log_info('MASTERSTUDY', 'Manual sync started');
        
        try {
            // 1. Check if MasterStudy LMS tables exist
            $posts_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->posts}'");
            if (!$posts_table_exists) {
                return array(
                    'success' => false,
                    'message' => 'MasterStudy LMS tables not found in database'
                );
            }
            
            // 2. Check if MasterStudy LMS is active
            if (!self::is_masterstudy_active()) {
                return array(
                    'success' => false,
                    'message' => 'MasterStudy LMS plugin is not active'
                );
            }
            
            // 3. Ensure our custom tables exist
            require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/activator.php';
            if (!VedMG_ClassRoom_Database_Activator::verify_tables_exist()) {
                // Create tables if they don't exist
                VedMG_ClassRoom_Database_Activator::activate();
                vedmg_log_info('MASTERSTUDY', 'Created missing custom tables during sync');
            }
            
            // 4. Get all MasterStudy courses with proper instructor handling
            // This query preserves original authorship and only uses fallbacks for truly missing data
            $masterstudy_courses = $wpdb->get_results("
                SELECT
                    p.ID as course_id,
                    p.post_title as course_name,
                    p.post_content as course_description,
                    p.post_author as post_author_id,
                    COALESCE(
                        NULLIF(pm_instructor.meta_value, ''),
                        p.post_author
                    ) as instructor_id,
                    CASE
                        WHEN pm_instructor.meta_value IS NOT NULL AND pm_instructor.meta_value != '' AND u_instructor.display_name IS NOT NULL
                        THEN u_instructor.display_name
                        WHEN u_author.display_name IS NOT NULL
                        THEN u_author.display_name
                        ELSE CONCAT('Deleted User (ID: ', p.post_author, ')')
                    END as instructor_name,
                    CASE
                        WHEN pm_instructor.meta_value IS NOT NULL AND pm_instructor.meta_value != '' AND u_instructor.user_email IS NOT NULL
                        THEN u_instructor.user_email
                        WHEN u_author.user_email IS NOT NULL
                        THEN u_author.user_email
                        ELSE ''
                    END as instructor_email,
                    p.post_status,
                    p.post_date as created_date,
                    p.post_modified as modified_date
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm_instructor ON (p.ID = pm_instructor.post_id AND pm_instructor.meta_key = 'course_instructor')
                LEFT JOIN {$wpdb->users} u_instructor ON pm_instructor.meta_value = u_instructor.ID
                LEFT JOIN {$wpdb->users} u_author ON p.post_author = u_author.ID
                WHERE p.post_type = 'stm-courses'
                AND p.post_status IN ('publish', 'draft', 'private')
                ORDER BY p.post_date DESC
            ");
            
            if (empty($masterstudy_courses)) {
                return array(
                    'success' => true,
                    'message' => 'No courses found in MasterStudy LMS',
                    'synced_count' => 0,
                    'updated_count' => 0,
                    'new_count' => 0
                );
            }
            
            $synced_count = 0;
            $updated_count = 0;
            $new_count = 0;
            
            // 5. Sync each course to our custom table
            foreach ($masterstudy_courses as $course) {
                $existing_course = $wpdb->get_row($wpdb->prepare("
                    SELECT course_id, updated_date FROM {$wpdb->prefix}vedmg_courses 
                    WHERE masterstudy_course_id = %d
                ", $course->course_id));
                
                $course_data = array(
                    'masterstudy_course_id' => $course->course_id,
                    'course_name' => $course->course_name,
                    'course_description' => $course->course_description,
                    'instructor_id' => $course->instructor_id ?: 0,
                    'instructor_name' => $course->instructor_name ?: 'Unknown Instructor',
                    'instructor_email' => $course->instructor_email ?: '',
                    'updated_date' => current_time('mysql')
                );
                
                if ($existing_course) {
                    // Update existing course
                    $wpdb->update(
                        $wpdb->prefix . 'vedmg_courses',
                        $course_data,
                        array('course_id' => $existing_course->course_id),
                        array('%d', '%s', '%s', '%d', '%s', '%s', '%s'),
                        array('%d')
                    );
                    $updated_count++;
                    vedmg_log_info('MASTERSTUDY', 'Updated course: ' . $course->course_name);
                } else {
                    // Insert new course
                    $course_data['created_date'] = current_time('mysql');
                    $course_data['classroom_status'] = 'pending';
                    $course_data['auto_enroll_enabled'] = 1;

                    $wpdb->insert(
                        $wpdb->prefix . 'vedmg_courses',
                        $course_data,
                        array('%d', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%d')
                    );
                    $new_count++;
                    vedmg_log_info('MASTERSTUDY', 'Created new course: ' . $course->course_name);
                }
                
                $synced_count++;
            }
            
            // 6. Update last sync time
            update_option('vedmg_classroom_last_sync_masterstudy', time());
            
            vedmg_log_info('MASTERSTUDY', "Manual sync completed: $synced_count courses processed ($new_count new, $updated_count updated)");
            
            return array(
                'success' => true,
                'message' => "Successfully synced $synced_count courses from MasterStudy LMS ($new_count new, $updated_count updated)",
                'synced_count' => $synced_count,
                'updated_count' => $updated_count,
                'new_count' => $new_count
            );
            
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Manual sync failed', $e->getMessage());
            return array(
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Handle automatic course publishing - create Google Classroom automatically
     */
    public static function on_course_published_auto($post_id, $post) {
        vedmg_log_info('MASTERSTUDY', "Course published automatically: $post_id");

        // Prevent duplicate processing
        $processing_key = 'vedmg_auto_creating_' . $post_id;
        if (get_transient($processing_key)) {
            vedmg_log_info('MASTERSTUDY', "Course $post_id already being auto-processed - skipping");
            return;
        }
        set_transient($processing_key, true, 120); // Prevent duplicates for 2 minutes

        // Check if auto-classroom creation is enabled
        $auto_create_classroom = get_option('vedmg_auto_create_classroom', true);

        if (!$auto_create_classroom) {
            vedmg_log_info('MASTERSTUDY', 'Auto classroom creation disabled');
            delete_transient($processing_key);
            return;
        }

        try {
            // First, sync the course to our database
            vedmg_log_info('MASTERSTUDY', "Attempting to sync course $post_id to VedMG database");
            $course_synced = self::sync_course_to_vedmg($post_id, 'published');

            if (!$course_synced) {
                vedmg_log_error('MASTERSTUDY', "Failed to sync course $post_id to VedMG database");
                delete_transient('vedmg_auto_creating_' . $post_id);
                return;
            }

            vedmg_log_info('MASTERSTUDY', "Successfully synced course $post_id to VedMG database");

            // Get course data from our database
            global $wpdb;
            $courses_table = $wpdb->prefix . 'vedmg_courses';

            $course_data = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $courses_table WHERE masterstudy_course_id = %d",
                $post_id
            ));

            if (!$course_data) {
                vedmg_log_error('MASTERSTUDY', "Course $post_id not found in VedMG database after sync");
                return;
            }

            // Check if classroom already exists
            if (!empty($course_data->google_classroom_id)) {
                vedmg_log_info('MASTERSTUDY', "Course $post_id already has classroom: " . $course_data->google_classroom_id);
                return;
            }

            // Check if instructor email is available and valid for API (@vedmg.in domain)
            $instructor_email = $course_data->instructor_email;

            if (empty($instructor_email) || strpos($instructor_email, '@vedmg.in') === false) {
                // Try to get from instructor sync table
                $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';
                $synced_instructor = $wpdb->get_row($wpdb->prepare(
                    "SELECT instructor_email FROM $sync_table WHERE wordpress_user_id = %d AND sync_status = 'active' LIMIT 1",
                    $course_data->instructor_id
                ));

                if ($synced_instructor && strpos($synced_instructor->instructor_email, '@vedmg.in') !== false) {
                    $instructor_email = $synced_instructor->instructor_email;
                    vedmg_log_info('MASTERSTUDY', "Using synced instructor email for course $post_id: $instructor_email");
                } else {
                    // No valid instructor email found - cannot create classroom
                    vedmg_log_error('MASTERSTUDY', "No valid instructor email found for course $post_id - cannot create classroom automatically");
                    delete_transient('vedmg_auto_creating_' . $post_id);
                    return;
                }
            }

            if (empty($instructor_email)) {
                vedmg_log_error('MASTERSTUDY', "Course $post_id has no instructor email and no fallback available - cannot create classroom");
                delete_transient('vedmg_auto_creating_' . $post_id);
                return;
            }

            // Create Google Classroom automatically
            $classroom_data = array(
                'name' => $course_data->course_name,
                'batch' => 'Auto-' . date('Y-m'),
                'descriptionHeading' => 'Automatically created classroom for: ' . $course_data->course_name,
                'location' => 'Online',
                'instructor_email' => $instructor_email
            );

            vedmg_log_info('MASTERSTUDY', "Creating classroom automatically for course $post_id");

            // Call the API to create classroom
            require_once plugin_dir_path(__FILE__) . '../api/api.php';
            $api_response = VedMG_ClassRoom_API::call_google_classroom_api('create_class', $classroom_data);

            if ($api_response && isset($api_response['id'])) {
                $calendar_id = isset($api_response['calendarId']) ? $api_response['calendarId'] : null;

                // If no calendar ID returned, use fallback calendar API
                if (empty($calendar_id)) {
                    vedmg_log_info('MASTERSTUDY', "No calendar ID in classroom response, using fallback API for course $post_id");

                    $calendar_api_data = array(
                        'class_name' => $course_data->course_name,
                        'instructor_name' => $instructor_email
                    );

                    $calendar_response = VedMG_ClassRoom_API::call_google_classroom_api('create_class_calendar', $calendar_api_data);

                    if ($calendar_response && isset($calendar_response['message'])) {
                        // Extract calendar ID from message
                        if (preg_match('/Created calendar with ID: (.+)/', $calendar_response['message'], $matches)) {
                            $calendar_id = $matches[1];
                            vedmg_log_info('MASTERSTUDY', "Fallback calendar API returned calendar ID: $calendar_id");
                        }
                    }
                }

                // Update course with classroom information
                $update_result = $wpdb->update(
                    $courses_table,
                    array(
                        'google_classroom_id' => $api_response['id'],
                        'calendar_id' => $calendar_id,
                        'google_classroom_link' => isset($api_response['alternateLink']) ? $api_response['alternateLink'] : '',
                        'classroom_status' => 'active',
                        'updated_date' => current_time('mysql')
                    ),
                    array('course_id' => $course_data->course_id),
                    array('%s', '%s', '%s', '%s', '%s'),
                    array('%d')
                );

                if ($update_result !== false) {
                    vedmg_log_info('MASTERSTUDY', "Classroom created automatically for course $post_id: " . $api_response['id'] . " with calendar: " . ($calendar_id ?: 'none'));
                } else {
                    vedmg_log_error('MASTERSTUDY', "Failed to update course $post_id with classroom data");
                }
            } else {
                $error_details = is_array($api_response) ? json_encode($api_response) : (string)$api_response;
                vedmg_log_error('MASTERSTUDY', "Failed to create classroom for course $post_id. API Response: $error_details");

                // Update course status to indicate API failure
                $wpdb->update(
                    $courses_table,
                    array(
                        'classroom_status' => 'api_failed',
                        'updated_date' => current_time('mysql')
                    ),
                    array('course_id' => $course_data->course_id),
                    array('%s', '%s'),
                    array('%d')
                );

                vedmg_log_info('MASTERSTUDY', "Course $post_id marked as api_failed - can be retried manually from admin panel");
            }

        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', "Exception in auto classroom creation for course $post_id: " . $e->getMessage());
            vedmg_log_error('MASTERSTUDY', "Exception stack trace: " . $e->getTraceAsString());
        } finally {
            // Always clean up the processing lock
            delete_transient('vedmg_auto_creating_' . $post_id);
        }
    }

    /**
     * Handle automatic course deletion - delete Google Classroom automatically
     */
    public static function on_course_deleted_auto($post_id, $post) {
        if ($post->post_type !== 'stm-courses') {
            return;
        }

        vedmg_log_info('MASTERSTUDY', "Course deleted automatically: $post_id");

        try {
            // Get course data from our database before deletion
            global $wpdb;
            $courses_table = $wpdb->prefix . 'vedmg_courses';

            $course_data = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $courses_table WHERE course_id = %d",
                $post_id
            ));

            if (!$course_data) {
                vedmg_log_info('MASTERSTUDY', "Course $post_id not found in VedMG database - nothing to clean up");
                return;
            }

            // If course has a Google Classroom, delete it
            if (!empty($course_data->google_classroom_id)) {
                vedmg_log_info('MASTERSTUDY', "Deleting classroom automatically for course $post_id: " . $course_data->google_classroom_id);

                // Get instructor email for API call
                $instructor_email = $course_data->instructor_email;

                if (empty($instructor_email)) {
                    // Try to get from instructor sync table
                    $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';
                    $instructor_sync = $wpdb->get_row($wpdb->prepare(
                        "SELECT instructor_email FROM $sync_table WHERE wordpress_user_id = %d",
                        $course_data->instructor_id
                    ));

                    if ($instructor_sync) {
                        $instructor_email = $instructor_sync->instructor_email;
                    }
                }

                if (!empty($instructor_email)) {
                    // First archive, then delete
                    require_once plugin_dir_path(__FILE__) . '../api/api.php';

                    // Archive classroom
                    $archive_response = VedMG_ClassRoom_API::call_google_classroom_api('archieve_course', array(
                        'course_id' => $course_data->google_classroom_id,
                        'instructor_email' => $instructor_email
                    ));

                    if ($archive_response) {
                        vedmg_log_info('MASTERSTUDY', "Classroom archived for deleted course $post_id");

                        // Delete classroom
                        $delete_response = VedMG_ClassRoom_API::call_google_classroom_api('delete_course', array(
                            'course_id' => $course_data->google_classroom_id,
                            'instructor_email' => $instructor_email
                        ));

                        if ($delete_response) {
                            vedmg_log_info('MASTERSTUDY', "Classroom deleted for deleted course $post_id");
                        } else {
                            vedmg_log_error('MASTERSTUDY', "Failed to delete classroom for course $post_id");
                        }
                    } else {
                        vedmg_log_error('MASTERSTUDY', "Failed to archive classroom for course $post_id");
                    }
                } else {
                    vedmg_log_error('MASTERSTUDY', "No instructor email found for course $post_id - cannot delete classroom");
                }
            }

            // Remove course from our database
            $delete_result = $wpdb->delete(
                $courses_table,
                array('course_id' => $post_id),
                array('%d')
            );

            if ($delete_result !== false) {
                vedmg_log_info('MASTERSTUDY', "Course $post_id removed from VedMG database");
            } else {
                vedmg_log_error('MASTERSTUDY', "Failed to remove course $post_id from VedMG database");
            }

        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', "Exception in auto course deletion for course $post_id: " . $e->getMessage());
        }
    }

    /**
     * Handle course status transitions
     */
    public static function on_course_status_transition($new_status, $old_status, $post) {
        if ($post->post_type !== 'stm-courses') {
            return;
        }

        vedmg_log_info('MASTERSTUDY', "Course {$post->ID} status changed from $old_status to $new_status");

        // Prevent duplicate processing - only trigger when publishing
        if ($new_status === 'publish' && $old_status !== 'publish') {
            // Use a transient to prevent duplicate processing
            $transient_key = 'vedmg_processing_course_' . $post->ID;
            if (get_transient($transient_key)) {
                vedmg_log_info('MASTERSTUDY', "Course {$post->ID} already being processed - skipping duplicate");
                return;
            }

            set_transient($transient_key, true, 60); // Prevent duplicates for 60 seconds
            self::on_course_published_auto($post->ID, $post);
            delete_transient($transient_key);
        }

        // If course is being unpublished, you might want to archive the classroom
        if ($old_status === 'publish' && $new_status !== 'publish') {
            // Optionally archive classroom when course is unpublished
            $auto_archive = get_option('vedmg_auto_archive_on_unpublish', false);
            if ($auto_archive) {
                self::auto_archive_classroom($post->ID);
            }
        }
    }

    /**
     * Auto archive classroom when course is unpublished
     */
    private static function auto_archive_classroom($course_id) {
        try {
            global $wpdb;
            $courses_table = $wpdb->prefix . 'vedmg_courses';

            $course_data = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $courses_table WHERE course_id = %d",
                $course_id
            ));

            if ($course_data && !empty($course_data->google_classroom_id) && !empty($course_data->instructor_email)) {
                require_once plugin_dir_path(__FILE__) . '../api/api.php';

                $archive_response = VedMG_ClassRoom_API::call_google_classroom_api('archieve_course', array(
                    'course_id' => $course_data->google_classroom_id,
                    'instructor_email' => $course_data->instructor_email
                ));

                if ($archive_response) {
                    $wpdb->update(
                        $courses_table,
                        array('classroom_status' => 'archived'),
                        array('course_id' => $course_id),
                        array('%s'),
                        array('%d')
                    );

                    vedmg_log_info('MASTERSTUDY', "Classroom auto-archived for unpublished course $course_id");
                }
            }
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', "Exception in auto archive for course $course_id: " . $e->getMessage());
        }
    }
}

// Initialize MasterStudy integration only if MasterStudy is active
add_action('plugins_loaded', function() {
    if (VedMG_ClassRoom_MasterStudy_Integration::is_masterstudy_active()) {
        VedMG_ClassRoom_MasterStudy_Integration::init();
        vedmg_log_info('INTEGRATION', 'MasterStudy LMS integration activated');
    } else {
        vedmg_log_warning('INTEGRATION', 'MasterStudy LMS not detected - integration disabled');
    }
});
