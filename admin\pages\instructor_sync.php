<?php
/**
 * VedMG ClassRoom Instructor Sync Page
 *
 * This page handles instructor synchronization from WordPress to VedMG ClassRoom.
 * Allows fetching instructors, managing sync status, and Google Classroom integration.
 *
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Prevent ALL forms of caching for this page
header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: 0");
header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
header("ETag: " . md5(time()));

// Add cache-busting meta tags
add_action("admin_head", function() {
    echo '<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">';
    echo '<meta http-equiv="Pragma" content="no-cache">';
    echo '<meta http-equiv="Expires" content="0">';
    echo '<meta name="cache-bust" content="' . time() . '">';
});



// Prevent browser caching of this page
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Include database helper
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';

// Log page access
vedmg_log_admin_action('Viewed instructor sync page');

// Handle pagination parameters
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = isset($_GET['per_page']) ? max(1, intval($_GET['per_page'])) : 10;
$search_term = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';

// Get instructor sync data from database with pagination
$instructors_data = vedmg_get_instructor_sync_paginated($current_page, $per_page, $search_term, $status_filter);
$instructors = $instructors_data['instructors'];
$total_instructors = $instructors_data['total'];
$total_pages = ceil($total_instructors / $per_page);

// Calculate pagination info
$start_item = (($current_page - 1) * $per_page) + 1;
$end_item = min($current_page * $per_page, $total_instructors);

// Build current URL for pagination links
$base_url = admin_url('admin.php?page=vedmg-classroom-instructor-sync');
$url_params = array();
if ($per_page != 10) $url_params['per_page'] = $per_page;
if ($search_term) $url_params['search'] = $search_term;
if ($status_filter) $url_params['status'] = $status_filter;
$base_url .= !empty($url_params) ? '&' . http_build_query($url_params) : '';

/**
 * Get instructor sync data with pagination
 */
function vedmg_get_instructor_sync_paginated($page = 1, $per_page = 10, $search = '', $status_filter = '') {
    global $wpdb;
    
    $sync_table = $wpdb->prefix . 'vedmg_instructor_sync';
    
    // Check if table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$sync_table'");
    if (!$table_exists) {
        return array(
            'instructors' => array(),
            'total' => 0,
            'page' => $page,
            'per_page' => $per_page
        );
    }
    
    // Sanitize parameters
    $page = max(1, intval($page));
    $per_page = max(1, min(100, intval($per_page)));
    
    // Build WHERE clause
    $where_conditions = array();
    $where_values = array();
    
    if (!empty($search)) {
        $search_term = '%' . $wpdb->esc_like($search) . '%';
        $where_conditions[] = "(instructor_name LIKE %s OR instructor_email LIKE %s OR instructor_phone LIKE %s)";
        $where_values[] = $search_term;
        $where_values[] = $search_term;
        $where_values[] = $search_term;
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "sync_status = %s";
        $where_values[] = $status_filter;
    }
    
    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }
    
    // Get total count
    $count_sql = "SELECT COUNT(*) FROM $sync_table $where_clause";
    if (!empty($where_values)) {
        $count_sql = $wpdb->prepare($count_sql, $where_values);
    }
    $total = intval($wpdb->get_var($count_sql));
    
    // Get paginated results
    $offset = ($page - 1) * $per_page;
    $sql = "SELECT * FROM $sync_table $where_clause ORDER BY last_synced_date DESC, created_date DESC LIMIT %d OFFSET %d";
    
    $values = array_merge($where_values, array($per_page, $offset));
    $sql = $wpdb->prepare($sql, $values);
    
    $instructors = $wpdb->get_results($sql);
    
    return array(
        'instructors' => $instructors,
        'total' => $total,
        'page' => $page,
        'per_page' => $per_page
    );
}
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>Instructor Sync Management</h1>
        <p>Sync instructors from WordPress and manage Google Classroom integration</p>
    </div>
    
    <!-- Sync Controls -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>Sync Controls</h2>
            <div class="vedmg-section-actions">
                <button class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="sync-instructors-btn">
                    <span class="dashicons dashicons-update"></span>
                    Sync Instructors from WordPress
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="sync-google-classroom-btn">
                    <span class="dashicons dashicons-google"></span>
                    Sync Google Classroom
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="refresh-data-btn">
                    <span class="dashicons dashicons-admin-page"></span>
                    Refresh Data
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-warning" id="cleanup-sync-btn">
                    <span class="dashicons dashicons-admin-tools"></span>
                    Cleanup Sync Data
                </button>
            </div>
        </div>
        
        <!-- Google Classroom Sync Controls -->
        <div class="vedmg-google-classroom-controls" style="display: none;">
            <div class="vedmg-instructor-selection">
                <label for="instructor-dropdown">Select Instructor:</label>
                <select id="instructor-dropdown" class="vedmg-search-select">
                    <option value="">-- Select Instructor --</option>
                </select>
            </div>
            <div class="vedmg-sync-actions">
                <button class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="start-google-sync" disabled>
                    <span class="dashicons dashicons-google"></span>
                    Start Google Classroom Sync
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-google-sync">
                    Cancel
                </button>
            </div>
        </div>
        
        <!-- Search and Filter Controls -->
        <form method="GET" class="vedmg-search-form">
            <input type="hidden" name="page" value="<?php echo esc_attr($_GET['page'] ?? ''); ?>">
            
            <div class="vedmg-search-controls">
                <div class="vedmg-search-group">
                    <label for="search">Search Instructors:</label>
                    <input type="text" name="search" id="search" 
                           value="<?php echo esc_attr($search_term); ?>" 
                           class="vedmg-search-input" 
                           placeholder="Search by name, email, or phone...">
                </div>
                
                <div class="vedmg-search-group">
                    <label for="status">Filter by Status:</label>
                    <select name="status" id="status" class="vedmg-search-select">
                        <option value="">All Statuses</option>
                        <option value="pending" <?php selected($status_filter, 'pending'); ?>>Pending</option>
                        <option value="synced" <?php selected($status_filter, 'synced'); ?>>Synced</option>
                        <option value="failed" <?php selected($status_filter, 'failed'); ?>>Failed</option>
                        <option value="disabled" <?php selected($status_filter, 'disabled'); ?>>Disabled</option>
                    </select>
                </div>
                
                <div class="vedmg-search-group">
                    <label for="per_page">Items per page:</label>
                    <select name="per_page" id="per_page" class="vedmg-search-select">
                        <option value="10" <?php selected($per_page, 10); ?>>10</option>
                        <option value="25" <?php selected($per_page, 25); ?>>25</option>
                        <option value="50" <?php selected($per_page, 50); ?>>50</option>
                        <option value="100" <?php selected($per_page, 100); ?>>100</option>
                    </select>
                </div>
                
                <div class="vedmg-search-group">
                    <button type="submit" class="vedmg-classroom-btn">Search</button>
                    <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-instructor-sync'); ?>" 
                       class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Clear</a>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Instructor Sync Table -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>Instructor Sync Status</h2>
            <div class="vedmg-section-info">
                Showing <?php echo $start_item; ?>-<?php echo $end_item; ?> of <?php echo $total_instructors; ?> instructors
            </div>
        </div>
        
        <div class="vedmg-table-container">
            <?php if (empty($instructors)): ?>
                <div class="vedmg-no-data">
                    <p>No instructors found. Click "Sync Instructors from WordPress" to get started.</p>
                </div>
            <?php else: ?>
                <table class="vedmg-classroom-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Sync Status</th>
                            <th>Last Synced</th>
                            <th>Google Status</th>
                            <th>Classrooms</th>
                            <th>Sync Google Classroom</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($instructors as $instructor): ?>
                            <tr>
                                <td>
                                    <strong><?php echo esc_html($instructor->instructor_name); ?></strong>
                                    <div class="row-actions">
                                        <span>ID: <?php echo esc_html($instructor->wordpress_user_id); ?></span>
                                    </div>
                                </td>
                                <td><?php echo esc_html($instructor->instructor_email); ?></td>
                                <td><?php echo esc_html($instructor->instructor_phone ?: 'N/A'); ?></td>
                                <td>
                                    <span class="vedmg-status vedmg-status-<?php echo esc_attr($instructor->sync_status); ?>">
                                        <?php echo esc_html(ucfirst($instructor->sync_status)); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($instructor->last_synced_date): ?>
                                        <?php echo esc_html(date('M j, Y H:i', strtotime($instructor->last_synced_date))); ?>
                                    <?php else: ?>
                                        <span class="vedmg-text-muted">Never</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="vedmg-status vedmg-status-<?php echo esc_attr($instructor->google_sync_status); ?>">
                                        <?php echo esc_html(str_replace('_', ' ', ucfirst($instructor->google_sync_status))); ?>
                                    </span>
                                    <?php if ($instructor->sync_error_message): ?>
                                        <div class="vedmg-error-message" title="<?php echo esc_attr($instructor->sync_error_message); ?>">
                                            <span class="dashicons dashicons-warning"></span>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="vedmg-badge">
                                        <?php echo esc_html($instructor->google_classroom_count); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-small sync-google-classroom-btn"
                                            data-instructor-id="<?php echo esc_attr($instructor->wordpress_user_id); ?>"
                                            data-instructor-name="<?php echo esc_attr($instructor->instructor_name); ?>"
                                            data-instructor-email="<?php echo esc_attr($instructor->instructor_email); ?>"
                                            <?php echo ($instructor->google_sync_status === 'syncing') ? 'disabled' : ''; ?>>
                                        <?php if ($instructor->google_sync_status === 'syncing'): ?>
                                            <span class="dashicons dashicons-update vedmg-spinning"></span>
                                            Syncing...
                                        <?php else: ?>
                                            <span class="dashicons dashicons-google"></span>
                                            Sync Google Classroom
                                        <?php endif; ?>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="vedmg-pagination">
                <div class="vedmg-pagination-info">
                    Page <?php echo $current_page; ?> of <?php echo $total_pages; ?>
                </div>
                <div class="vedmg-pagination-links">
                    <?php if ($current_page > 1): ?>
                        <a href="<?php echo $base_url; ?>&paged=1" class="vedmg-pagination-link">« First</a>
                        <a href="<?php echo $base_url; ?>&paged=<?php echo ($current_page - 1); ?>" class="vedmg-pagination-link">‹ Previous</a>
                    <?php endif; ?>
                    
                    <?php
                    $start_page = max(1, $current_page - 2);
                    $end_page = min($total_pages, $current_page + 2);
                    
                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                        <?php if ($i == $current_page): ?>
                            <span class="vedmg-pagination-link vedmg-pagination-current"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="<?php echo $base_url; ?>&paged=<?php echo $i; ?>" class="vedmg-pagination-link"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($current_page < $total_pages): ?>
                        <a href="<?php echo $base_url; ?>&paged=<?php echo ($current_page + 1); ?>" class="vedmg-pagination-link">Next ›</a>
                        <a href="<?php echo $base_url; ?>&paged=<?php echo $total_pages; ?>" class="vedmg-pagination-link">Last »</a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- CSS Styles -->
<style>
/* Instructor Sync Page Styles */
.vedmg-classroom-admin {
    max-width: 1200px;
    margin: 20px 0;
}

.vedmg-classroom-header {
    margin-bottom: 30px;
}

.vedmg-classroom-header h1 {
    font-size: 28px;
    margin: 0 0 10px 0;
    color: #23282d;
}

.vedmg-classroom-header p {
    font-size: 16px;
    color: #666;
    margin: 0;
}

.vedmg-classroom-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: box-shadow 0.2s ease;
}

.vedmg-classroom-section:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.vedmg-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.vedmg-section-header h2 {
    font-size: 20px;
    margin: 0;
    color: #23282d;
    font-weight: 600;
}

.vedmg-section-actions {
    display: flex;
    gap: 10px;
}

.vedmg-section-info {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #0073aa;
}

/* Button Styles */
.vedmg-classroom-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.4;
    border: 1px solid;
    border-radius: 4px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.vedmg-classroom-btn-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.vedmg-classroom-btn-primary:hover {
    background: #005a87;
    border-color: #005a87;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,115,170,0.3);
}

.vedmg-classroom-btn-secondary {
    background: #f7f7f7;
    border-color: #ccd0d4;
    color: #555;
}

.vedmg-classroom-btn-secondary:hover {
    background: #fafafa;
    border-color: #999;
    color: #333;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.vedmg-classroom-btn-small {
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
}

.vedmg-classroom-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.vedmg-classroom-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Search Form */
.vedmg-search-form {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 0;
}

.vedmg-search-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: end;
}

.vedmg-search-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.vedmg-search-group label {
    font-size: 12px;
    font-weight: 600;
    color: #555;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-search-input,
.vedmg-search-select {
    padding: 8px 12px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    font-size: 14px;
    min-width: 150px;
    transition: all 0.2s ease;
    background: #fff;
}

.vedmg-search-input:focus,
.vedmg-search-select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
    outline: none;
    background: #fff;
}

.vedmg-search-input:hover,
.vedmg-search-select:hover {
    border-color: #999;
}

.vedmg-search-input::placeholder {
    color: #999;
    font-style: italic;
}

/* Table Styles */
.vedmg-table-container {
    overflow-x: auto;
    border-radius: 6px;
    border: 1px solid #ddd;
    background: #fff;
}

.vedmg-classroom-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    background: #fff;
}

.vedmg-classroom-table th,
.vedmg-classroom-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.vedmg-classroom-table th {
    background: #f9f9f9;
    font-weight: 600;
    color: #555;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #ddd;
    position: sticky;
    top: 0;
    z-index: 10;
}

.vedmg-classroom-table tbody tr {
    transition: all 0.2s ease;
}

.vedmg-classroom-table tbody tr:hover {
    background: #f8f9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vedmg-classroom-table tbody tr:hover td {
    border-color: #ccd6f6;
}

.row-actions {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
    font-style: italic;
}

.vedmg-classroom-table tbody tr:hover .row-actions {
    color: #666;
}

/* Enhanced hover effects for table cells */
.vedmg-classroom-table td:first-child {
    font-weight: 500;
}

.vedmg-classroom-table tbody tr:hover td:first-child strong {
    color: #0073aa;
}

/* Email cell hover effect */
.vedmg-classroom-table td:nth-child(2) {
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.vedmg-classroom-table tbody tr:hover td:nth-child(2) {
    color: #0073aa;
    font-weight: 500;
}

/* Status Styles */
.vedmg-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
    cursor: default;
}

.vedmg-status:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.vedmg-status-pending {
    background: #fef7e0;
    color: #b26e00;
    border: 1px solid #f4d03f;
}

.vedmg-status-pending:hover {
    background: #fdeaa7;
    color: #8b5a00;
}

.vedmg-status-synced {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

.vedmg-status-synced:hover {
    background: #c8e6c9;
    color: #1b5e20;
}

.vedmg-status-failed {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ef9a9a;
}

.vedmg-status-failed:hover {
    background: #ffcdd2;
    color: #b71c1c;
}

.vedmg-status-disabled {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ccc;
}

.vedmg-status-disabled:hover {
    background: #e0e0e0;
    color: #424242;
}

.vedmg-status-not_synced {
    background: #e3f2fd;
    color: #1565c0;
    border: 1px solid #90caf9;
}

.vedmg-status-not_synced:hover {
    background: #bbdefb;
    color: #0d47a1;
}

.vedmg-status-syncing {
    background: #fff3e0;
    color: #e65100;
    border: 1px solid #ffcc02;
}

.vedmg-status-syncing:hover {
    background: #ffe0b2;
    color: #bf360c;
}

/* Badge Styles */
.vedmg-badge {
    display: inline-block;
    padding: 4px 8px;
    background: #e3f2fd;
    color: #1565c0;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid #90caf9;
    transition: all 0.2s ease;
    cursor: default;
}

.vedmg-badge:hover {
    background: #bbdefb;
    color: #0d47a1;
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(21,101,192,0.3);
}

/* Error Message */
.vedmg-error-message {
    display: inline-block;
    margin-left: 5px;
    color: #c62828;
    cursor: help;
    transition: all 0.2s ease;
}

.vedmg-error-message:hover {
    color: #b71c1c;
    transform: scale(1.2);
}

.vedmg-error-message .dashicons {
    font-size: 16px;
}

/* Text Styles */
.vedmg-text-muted {
    color: #999;
    font-style: italic;
    transition: color 0.2s ease;
}

.vedmg-classroom-table tbody tr:hover .vedmg-text-muted {
    color: #666;
}

/* No Data */
.vedmg-no-data {
    text-align: center;
    padding: 40px;
    color: #666;
}

.vedmg-no-data p {
    font-size: 16px;
    margin: 0;
}

/* Pagination */
.vedmg-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.vedmg-pagination-info {
    font-size: 14px;
    color: #666;
}

.vedmg-pagination-links {
    display: flex;
    gap: 5px;
}

.vedmg-pagination-link {
    display: inline-block;
    padding: 6px 12px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    text-decoration: none;
    color: #0073aa;
    font-size: 14px;
}

.vedmg-pagination-link:hover {
    background: #f7f7f7;
    border-color: #999;
}

.vedmg-pagination-current {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

/* Google Classroom Controls */
.vedmg-google-classroom-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #0073aa;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.vedmg-instructor-selection {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.vedmg-instructor-selection label {
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    min-width: 130px;
}

.vedmg-sync-actions {
    display: flex;
    gap: 10px;
}

.vedmg-google-classroom-controls select {
    min-width: 300px;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: #fff;
    transition: all 0.2s ease;
}

.vedmg-google-classroom-controls select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
    outline: none;
}

.vedmg-google-classroom-controls select:hover {
    border-color: #999;
}
.vedmg-spinning {
    animation: vedmg-spin 1s linear infinite;
}

@keyframes vedmg-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .vedmg-search-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .vedmg-search-group {
        flex-direction: row;
        align-items: center;
        gap: 10px;
    }
    
    .vedmg-search-group label {
        min-width: 100px;
    }
    
    .vedmg-section-actions {
        flex-direction: column;
    }
    
    .vedmg-pagination {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}
</style>

<!-- JavaScript -->
<script>
jQuery(document).ready(function($) {
    // Get nonce for AJAX requests
    var vedmgNonce = '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>';
    
    // Load instructors on page load
    loadInstructors();
    
    // Sync Instructors button
    $('#sync-instructors-btn').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.html();
        
        // Disable button and show loading
        $btn.prop('disabled', true).html('<span class="dashicons dashicons-update vedmg-spinning"></span> Syncing...');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_sync_instructors',
                nonce: vedmgNonce
            },
            success: function(response) {
                if (response.success) {
                    // Force hard refresh with cache busting to show updated data
                    forcePageRefreshEnhanced();
                } else {
                    alert('Sync failed: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                alert('AJAX Error: ' + error);
                console.error('Sync error:', xhr.responseText);
            },
            complete: function() {
                // Re-enable button
                $btn.prop('disabled', false).html(originalText);
            }
        });
    });
    
    // Google Classroom Sync button
    $('#sync-google-classroom-btn').on('click', function() {
        var $controls = $('.vedmg-google-classroom-controls');
        if ($controls.is(':visible')) {
            $controls.slideUp();
        } else {
            $controls.slideDown();
        }
    });
    
    // Cancel Google Classroom sync
    $('#cancel-google-sync').on('click', function() {
        $('.vedmg-google-classroom-controls').slideUp();
        $('#instructor-dropdown').val('');
        $('#start-google-sync').prop('disabled', true);
    });
    
    // Handle instructor dropdown change
    $('#instructor-dropdown').on('change', function() {
        const selectedInstructorId = $(this).val();
        $('#start-google-sync').prop('disabled', !selectedInstructorId);
    });
    
    // Start Google Classroom sync
    $('#start-google-sync').on('click', function() {
        const selectedInstructorId = $('#instructor-dropdown').val();
        const selectedOption = $('#instructor-dropdown option:selected');
        const selectedInstructorName = selectedOption.data('name');
        const selectedInstructorEmail = selectedOption.data('email');
        
        if (!selectedInstructorId) {
            alert('Please select an instructor first.');
            return;
        }
        
        if (!selectedInstructorEmail) {
            alert('Selected instructor does not have an email address.');
            return;
        }
        
        console.log('Starting Google Classroom sync for:', {
            instructor_id: selectedInstructorId,
            instructor_name: selectedInstructorName,
            instructor_email: selectedInstructorEmail
        });
        
        syncGoogleClassroom(selectedInstructorEmail, selectedInstructorName, selectedInstructorId);
    });
    
    // Refresh Data button
    $('#refresh-data-btn').on('click', function() {
        // Force hard refresh with cache busting
        forcePageRefreshEnhanced();
    });

    // Cleanup Sync Data button
    $('#cleanup-sync-btn').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.html();

        if (!confirm('This will clean up the instructor sync table by:\n\n• Removing entries for deleted WordPress users\n• Removing duplicate entries\n\nThis action cannot be undone. Continue?')) {
            return;
        }

        // Disable button and show loading
        $btn.prop('disabled', true).html('<span class="dashicons dashicons-admin-tools vedmg-spinning"></span> Cleaning up...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_cleanup_instructor_sync',
                nonce: vedmgNonce
            },
            success: function(response) {
                if (response.success) {
                    alert('✅ ' + response.data.message);
                    // Force hard refresh with cache busting to show updated data
                    forcePageRefreshEnhanced();
                } else {
                    alert('❌ Cleanup failed: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                alert('❌ AJAX Error: ' + error);
                console.error('Cleanup error:', xhr.responseText);
            },
            complete: function() {
                // Re-enable button
                $btn.prop('disabled', false).html(originalText);
            }
        });
    });
    
    // Individual Google Classroom sync buttons
    $('.sync-google-classroom-btn').on('click', function() {
        var $btn = $(this);
        var instructorId = $btn.data('instructor-id');
        var instructorName = $btn.data('instructor-name');
        var instructorEmail = $btn.data('instructor-email');
        var originalText = $btn.html();
        
        if (!confirm('Sync Google Classroom for ' + instructorName + '?\n\nThis will:\n• Match Google Classroom courses with existing database courses\n• Update course statuses to "active" where matches are found\n• Create new courses for unmatched Google Classroom courses')) {
            return;
        }
        
        if (!instructorEmail) {
            alert('❌ Instructor email is missing. Cannot sync.\n\nPlease ensure this instructor has a valid email address.');
            return;
        }
        
        console.log('Individual sync for:', {
            instructor_id: instructorId,
            instructor_name: instructorName,
            instructor_email: instructorEmail
        });
        
        // Update the current row to show syncing status
        var $row = $btn.closest('tr');
        var $statusBadge = $row.find('.vedmg-status');
        var $classroomCount = $row.find('.vedmg-badge');
        var originalStatus = $statusBadge.text();
        
        // Show syncing status immediately
        $statusBadge.removeClass().addClass('vedmg-status vedmg-status-syncing').text('Syncing');
        $btn.prop('disabled', true).html('<span class="dashicons dashicons-update vedmg-spinning"></span> Syncing...');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_sync_google_classroom',
                instructor_email: instructorEmail,
                instructor_name: instructorName,
                instructor_id: instructorId,
                nonce: vedmgNonce
            },
            success: function(response) {
                if (response.success) {
                    // Update UI immediately without page reload
                    $statusBadge.removeClass().addClass('vedmg-status vedmg-status-synced').text('Synced');

                    // Update classroom count with the number of Google Classroom courses found
                    if (response.data.google_courses !== undefined) {
                        $classroomCount.text(response.data.google_courses);
                    }

                    // Show detailed success message with Google Classroom details
                    alert(response.data.message);

                    console.log('Sync completed:', response.data);

                    // Verify database update was successful before refreshing
                    if (response.data.database_updated === true) {
                        // Force page refresh after a delay to ensure database changes are reflected
                        setTimeout(function() {
                            forcePageRefreshEnhanced();
                        }, 2000);
                    } else {
                        // If database update verification failed, still refresh but with longer delay
                        console.warn('Database update verification not confirmed, refreshing with longer delay');
                        setTimeout(function() {
                            forcePageRefreshEnhanced();
                        }, 4000);
                    }
                } else {
                    // Restore original status on error
                    $statusBadge.removeClass().addClass('vedmg-status vedmg-status-failed').text('Failed');
                    alert('❌ Sync failed: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                // Restore original status on error
                $statusBadge.removeClass().addClass('vedmg-status vedmg-status-failed').text('Failed');
                alert('❌ AJAX Error: ' + error + '\n\nPlease check your internet connection and try again.');
                console.error('Google sync error:', xhr.responseText);
            },
            complete: function() {
                // Re-enable button
                $btn.prop('disabled', false).html(originalText);
            }
        });
    });
    
    /**
     * Load instructors for dropdown
     */
    function loadInstructors() {
        const $dropdown = $('#instructor-dropdown');
        
        // Show loading state
        $dropdown.html('<option value="">Loading instructors...</option>').prop('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_get_all_instructors',
                nonce: vedmgNonce
            },
            success: function(response) {
                if (response.success && response.data.instructors) {
                    populateInstructorDropdown(response.data.instructors);
                } else {
                    console.error('Failed to load instructors:', response.data);
                    $dropdown.html('<option value="">Error loading instructors</option>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error loading instructors:', error);
                $dropdown.html('<option value="">Error loading instructors</option>');
            },
            complete: function() {
                $dropdown.prop('disabled', false);
            }
        });
    }
    
    /**
     * Populate instructor dropdown with data
     */
    function populateInstructorDropdown(instructors) {
        const $dropdown = $('#instructor-dropdown');
        
        // Clear existing options
        $dropdown.empty();
        
        // Add default option
        $dropdown.append('<option value="">-- Select Instructor --</option>');
        
        // Add instructor options
        if (instructors && instructors.length > 0) {
            instructors.forEach(function(instructor) {
                const optionText = `${instructor.instructor_name} (${instructor.instructor_email})`;
                $dropdown.append(`<option value="${instructor.instructor_id}" data-email="${instructor.instructor_email}" data-name="${instructor.instructor_name}">${optionText}</option>`);
            });
        } else {
            $dropdown.append('<option value="">No instructors found</option>');
        }
    }
    
    /**
     * Sync Google Classroom for selected instructor
     */
    function syncGoogleClassroom(instructorEmail, instructorName, instructorId) {
        const $button = $('#start-google-sync');
        const originalText = $button.text();
        
        // Show loading state
        $button.addClass('vedmg-loading').prop('disabled', true).text('Syncing...');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_sync_google_classroom',
                instructor_email: instructorEmail,
                instructor_name: instructorName,
                instructor_id: instructorId,
                nonce: vedmgNonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    let message = `Google Classroom sync completed successfully!\n\n` +
                                `Instructor: ${data.instructor_name}\n` +
                                `Email: ${data.instructor_email}\n` +
                                `Database courses found: ${data.database_courses}\n` +
                                `Google Classroom courses: ${data.google_courses}\n` +
                                `Matches found: ${data.matches_found}\n` +
                                `Courses updated: ${data.courses_updated}\n` +
                                `New courses created: ${data.created || 0}`;
                    
                    if (data.matched_details && data.matched_details.length > 0) {
                        message += `\n\nCourse Actions:\n`;
                        data.matched_details.forEach(function(detail, index) {
                            const actionText = detail.action === 'created' ? 'Created' : 'Updated';
                            message += `${index + 1}. ${actionText}: "${detail.google_course}"`;
                            if (detail.action === 'updated') {
                                message += ` (matched with "${detail.db_course}")`;
                            }
                            message += `\n`;
                        });
                    }
                    
                    alert(message);
                    
                    // Hide the controls and force a hard refresh with cache busting
                    $('.vedmg-google-classroom-controls').slideUp();
                    setTimeout(function() {
                        // Force hard refresh with cache busting
                        forcePageRefreshEnhanced();
                    }, 2000);
                } else {
                    alert('Sync failed: ' + (response.data || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error during sync:', error);
                alert('Sync failed due to network error. Please try again.');
            },
            complete: function() {
                // Re-enable button
                $button.removeClass('vedmg-loading').prop('disabled', false).text(originalText);
            }
        });
    }
    
    // Auto-refresh page every 30 seconds if there are syncing operations
    if ($('.vedmg-status-syncing').length > 0) {
        setTimeout(function() {
            location.reload();
        }, 30000);
    }

    /**
     * Force page refresh with proper cache busting
     */
    /**
     * Enhanced cache busting and page refresh
     */
    function forcePageRefreshEnhanced() {
        // Clear all possible caches
        if ("caches" in window) {
            caches.keys().then(function(names) {
                names.forEach(function(name) {
                    caches.delete(name);
                });
            });
        }
        
        // Clear localStorage and sessionStorage
        try {
            localStorage.clear();
            sessionStorage.clear();
        } catch(e) {
            console.log("Storage clear failed:", e);
        }
        
        // Build cache-busted URL
        let url = window.location.href;
        
        // Remove existing cache-busting parameters
        url = url.replace(/[?&](refresh|cache_bust|_t|timestamp)=\d+/g, "");
        
        // Add multiple cache-busting parameters
        const separator = url.indexOf("?") > -1 ? "&" : "?";
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(7);
        
        const newUrl = url + separator + 
            "refresh=" + timestamp + 
            "&cache_bust=" + random + 
            "&_t=" + timestamp + 
            "&v=" + Math.floor(timestamp / 1000);
        
        console.log("Force refreshing to:", newUrl);
        
        // Use location.replace to prevent back button issues
        window.location.replace(newUrl);
    }
    
    /**
     * Verify sync status from server
     */
    function verifyInstructorSyncStatus(instructorEmail, expectedStatus) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: ajaxurl,
                type: "POST",
                data: {
                    action: "vedmg_verify_instructor_sync_status",
                    instructor_email: instructorEmail,
                    nonce: vedmgNonce
                },
                success: function(response) {
                    if (response.success) {
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function(xhr, status, error) {
                    reject(error);
                }
            });
        });
    }
});
</script>
