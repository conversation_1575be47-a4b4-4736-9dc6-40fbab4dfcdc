<?php
/**
 * VedMG ClassRoom WooCommerce Integration
 * 
 * This file handles WooCommerce order completion hooks and automatic
 * student enrollment into Google Classroom based on course purchases.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom WooCommerce Integration Class
 * 
 * Handles integration with WooCommerce for automatic enrollment
 */
class VedMG_ClassRoom_WooCommerce_Integration {
    
    /**
     * Initialize WooCommerce integration
     */
    public static function init() {
        // Hook into WooCommerce order status changes
        add_action('woocommerce_order_status_completed', array(__CLASS__, 'handle_order_completed'), 10, 1);
        add_action('woocommerce_order_status_processing', array(__CLASS__, 'handle_order_processing'), 10, 1);

        // Additional hooks for different payment methods and order statuses
        add_action('woocommerce_order_status_on-hold', array(__CLASS__, 'handle_order_on_hold'), 10, 1);
        add_action('woocommerce_order_status_pending', array(__CLASS__, 'handle_order_pending'), 10, 1);

        // Hook into order creation/payment completion for immediate processing
        add_action('woocommerce_thankyou', array(__CLASS__, 'handle_order_thankyou'), 10, 1);
        add_action('woocommerce_payment_complete', array(__CLASS__, 'handle_payment_complete'), 10, 1);

        // Hook into order status changes (catch-all)
        add_action('woocommerce_order_status_changed', array(__CLASS__, 'handle_order_status_changed'), 10, 4);

        vedmg_log_info('WOOCOMMERCE', 'WooCommerce integration initialized with comprehensive order hooks');
    }
    
    /**
     * Handle completed order
     * Automatically enroll students when order is completed
     */
    public static function handle_order_completed($order_id) {
        vedmg_log_info('WOOCOMMERCE', 'Processing completed order: ' . $order_id);
        
        try {
            self::process_order_enrollment($order_id, 'completed');
        } catch (Exception $e) {
            vedmg_log_error('WOOCOMMERCE', 'Failed to process completed order', $e->getMessage());
        }
    }
    
    /**
     * Handle processing order
     * Prepare enrollment data when order is processing
     */
    public static function handle_order_processing($order_id) {
        vedmg_log_info('WOOCOMMERCE', 'Processing order: ' . $order_id);

        try {
            self::process_order_enrollment($order_id, 'processing');
        } catch (Exception $e) {
            vedmg_log_error('WOOCOMMERCE', 'Failed to process order', $e->getMessage());
        }
    }

    /**
     * Handle order on-hold status (common for Cash on Delivery)
     */
    public static function handle_order_on_hold($order_id) {
        vedmg_log_info('WOOCOMMERCE', 'Order on hold: ' . $order_id);

        try {
            self::process_order_enrollment($order_id, 'on-hold');
        } catch (Exception $e) {
            vedmg_log_error('WOOCOMMERCE', 'Failed to process on-hold order', $e->getMessage());
        }
    }

    /**
     * Handle order pending status
     */
    public static function handle_order_pending($order_id) {
        vedmg_log_info('WOOCOMMERCE', 'Order pending: ' . $order_id);

        try {
            self::process_order_enrollment($order_id, 'pending');
        } catch (Exception $e) {
            vedmg_log_error('WOOCOMMERCE', 'Failed to process pending order', $e->getMessage());
        }
    }

    /**
     * Handle order thank you page (immediate after checkout)
     */
    public static function handle_order_thankyou($order_id) {
        if (!$order_id) return;
        vedmg_log_info('WOOCOMMERCE', 'Order thank you page: ' . $order_id);

        try {
            self::process_order_enrollment($order_id, 'thankyou');
        } catch (Exception $e) {
            vedmg_log_error('WOOCOMMERCE', 'Failed to process thankyou order', $e->getMessage());
        }
    }

    /**
     * Handle payment complete
     */
    public static function handle_payment_complete($order_id) {
        vedmg_log_info('WOOCOMMERCE', 'Payment completed for order: ' . $order_id);

        try {
            self::process_order_enrollment($order_id, 'payment_complete');
        } catch (Exception $e) {
            vedmg_log_error('WOOCOMMERCE', 'Failed to process payment complete', $e->getMessage());
        }
    }

    /**
     * Handle any order status change (catch-all)
     */
    public static function handle_order_status_changed($order_id, $old_status, $new_status, $order) {
        vedmg_log_info('WOOCOMMERCE', "Order status changed: $order_id from '$old_status' to '$new_status'");

        // Process order for statuses that indicate a purchase has been made
        $processable_statuses = array('processing', 'completed', 'on-hold', 'pending-payment', 'pending');
        if (in_array($new_status, $processable_statuses)) {
            try {
                self::process_order_enrollment($order_id, $new_status);
            } catch (Exception $e) {
                vedmg_log_error('WOOCOMMERCE', "Failed to process status change to $new_status", $e->getMessage());
            }
        }
    }

    /**
     * Process order enrollment
     */
    private static function process_order_enrollment($order_id, $status) {
        // Get order object
        $order = wc_get_order($order_id);
        if (!$order) {
            throw new Exception('Order not found: ' . $order_id);
        }
        
        // Get customer data
        $customer_data = self::extract_customer_data($order);
        
        // Get course data from order items
        $course_purchases = self::extract_course_purchases($order);
        
        if (empty($course_purchases)) {
            vedmg_log_info('WOOCOMMERCE', 'No course purchases found in order: ' . $order_id);
            return;
        }
        
        // Process each course purchase
        foreach ($course_purchases as $course_purchase) {
            self::enroll_student_in_course($customer_data, $course_purchase, $status, $order_id);
        }
        
        vedmg_log_info('WOOCOMMERCE', 'Processed ' . count($course_purchases) . ' course enrollments for order: ' . $order_id);
    }
    
    /**
     * Extract customer data from order
     */
    private static function extract_customer_data($order) {
        $full_name = trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name());

        // If billing name is empty, try to get WordPress user display name
        if (empty($full_name) && $order->get_user_id()) {
            $user = get_userdata($order->get_user_id());
            if ($user) {
                $full_name = $user->display_name ?: $user->user_login;
            }
        }

        // Final fallback to email prefix if still empty
        if (empty($full_name) && $order->get_billing_email()) {
            $email_parts = explode('@', $order->get_billing_email());
            $full_name = ucfirst($email_parts[0]);
        }

        return array(
            'user_id' => $order->get_user_id(),
            'first_name' => $order->get_billing_first_name(),
            'last_name' => $order->get_billing_last_name(),
            'email' => $order->get_billing_email(),
            'phone' => $order->get_billing_phone(),
            'address' => $order->get_billing_address_1(),
            'city' => $order->get_billing_city(),
            'country' => $order->get_billing_country(),
            'full_name' => $full_name
        );
    }
    
    /**
     * Extract course purchases from order items
     * Robust detection using (in order):
     * 1) Order item meta (_stm_lms_course_id, stm_lms_course_id, vedmg_course_id, course_id)
     * 2) Product meta (_stm_lms_course_id)
     * 3) Our courses table mapping by woocommerce_product_id
     * 4) Fallback: exact name match between product name and course_name
     */
    private static function extract_course_purchases($order) {
        global $wpdb;
        $course_purchases = array();

        foreach ($order->get_items() as $item_id => $item) {
            $product_id = $item->get_product_id();
            $product_name = $item->get_name();

            // 1) Try to read course mapping from order item meta
            $possible_meta_keys = array('_stm_lms_course_id', 'stm_lms_course_id', 'vedmg_course_id', 'course_id');
            $found_course_id = null;
            foreach ($possible_meta_keys as $meta_key) {
                $val = $item->get_meta($meta_key, true);
                if (!empty($val)) {
                    $found_course_id = intval($val);
                    break;
                }
            }

            // 2) If not found on item, check product meta for multiple possible keys
            if (empty($found_course_id)) {
                $product_meta_keys = array('_stm_lms_course_id', 'vedmg_linked_course_id', 'stm_lms_course_id', 'course_id');
                foreach ($product_meta_keys as $meta_key) {
                    $val = get_post_meta($product_id, $meta_key, true);
                    if (!empty($val)) {
                        $found_course_id = intval($val);
                        vedmg_log_info('WOOCOMMERCE', "Found course ID via product meta '$meta_key': Product $product_id → Course $found_course_id");
                        break;
                    }
                }
            }

            // 3) If still not found, check our own courses table mapping by Woo product ID
            if (empty($found_course_id)) {
                $course_row = self::get_course_row_by_product_id($product_id);
                if ($course_row && !empty($course_row->masterstudy_course_id)) {
                    $found_course_id = intval($course_row->masterstudy_course_id);
                }
            }

            // 4) Fallback: check Vedmg-woo-LMS plugin mapping table directly
            if (empty($found_course_id)) {
                $vedmg_woo_course_id = self::get_course_from_vedmg_woo_lms_mapping($product_id);
                if (!empty($vedmg_woo_course_id)) {
                    $found_course_id = intval($vedmg_woo_course_id);
                    vedmg_log_info('WOOCOMMERCE', "Matched product to course via Vedmg-woo-LMS mapping: Product ID $product_id → Course ID {$found_course_id}");
                }
            }

            // 5) Final fallback: match by name in our courses table
            if (empty($found_course_id) && !empty($product_name)) {
                $course_row = self::get_course_row_by_name($product_name);
                if ($course_row && !empty($course_row->masterstudy_course_id)) {
                    $found_course_id = intval($course_row->masterstudy_course_id);
                    vedmg_log_info('WOOCOMMERCE', "Matched product to course by name: '$product_name' → {$course_row->course_name} (MS ID: {$found_course_id})");
                }
            }

            // If we found a course mapping, verify it exists in our database and has calendar_id
            if (!empty($found_course_id)) {
                $course_info = self::get_course_info($found_course_id);

                if ($course_info) {
                    // Only add to purchases if course exists in our database
                    $course_purchases[] = array(
                        'product_id' => $product_id,
                        'course_id' => $found_course_id,
                        'course_name' => $product_name,
                        'quantity' => $item->get_quantity(),
                        'total' => $item->get_total(),
                        'course_info' => $course_info  // Include course info for calendar sharing decision
                    );
                } else {
                    vedmg_log_warning('WOOCOMMERCE', "Course ID $found_course_id found for product '$product_name' but course not in VedMG database - skipping");
                }
            } else {
                vedmg_log_info('WOOCOMMERCE', "Item '$product_name' (Product ID: $product_id) not recognized as course - no mapping found");
            }
        }

        return $course_purchases;
    }

    /**
     * Check if product is a MasterStudy course
     */
    private static function is_masterstudy_course($product_id) {
        // Check if product has MasterStudy course meta
        $course_id = get_post_meta($product_id, '_stm_lms_course_id', true);
        return !empty($course_id);
    }

    /**
     * Get MasterStudy course ID from product
     */
    private static function get_masterstudy_course_id($product_id) {
        return get_post_meta($product_id, '_stm_lms_course_id', true);
    }

    /**
     * Get our course row by Woo product ID (from vedmg_courses.woocommerce_product_id)
     */
    private static function get_course_row_by_product_id($product_id) {
        global $wpdb;
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM {$wpdb->prefix}vedmg_courses WHERE woocommerce_product_id = %d", $product_id));
    }

    /**
     * Get our course row by exact course name match
     */
    private static function get_course_row_by_name($name) {
        global $wpdb;
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM {$wpdb->prefix}vedmg_courses WHERE course_name = %s", $name));
    }

    /**
     * Get course ID from Vedmg-woo-LMS plugin mapping table
     * This is the most robust fallback - directly queries the mapping plugin's table
     * to find which course is linked to which WooCommerce product
     */
    private static function get_course_from_vedmg_woo_lms_mapping($product_id) {
        global $wpdb;

        vedmg_log_info('WOOCOMMERCE', "Checking Vedmg-woo-LMS mapping for product ID: $product_id");

        // The actual table name from Vedmg-woo-LMS plugin (found in database/Activator.php)
        $table_name = $wpdb->prefix . 'linking_table';

        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table_name));

        if (!$table_exists) {
            vedmg_log_info('WOOCOMMERCE', "Vedmg-woo-LMS table '$table_name' does not exist");
            return null;
        }

        vedmg_log_info('WOOCOMMERCE', "Found Vedmg-woo-LMS table: $table_name");

        try {
            // Query the linking table for course mapping
            // Table structure: id, course_id (MasterStudy course ID), product_id (WooCommerce product ID), type
            $query = $wpdb->prepare(
                "SELECT course_id FROM $table_name WHERE product_id = %d AND type = 'course' LIMIT 1",
                $product_id
            );

            vedmg_log_info('WOOCOMMERCE', "Querying Vedmg-woo-LMS: $query");

            $course_id = $wpdb->get_var($query);

            if (!empty($course_id)) {
                vedmg_log_info('WOOCOMMERCE', "✅ Found mapping in Vedmg-woo-LMS: Product $product_id → Course $course_id");
                return intval($course_id);
            } else {
                vedmg_log_info('WOOCOMMERCE', "No mapping found in Vedmg-woo-LMS for product ID: $product_id");
            }

        } catch (Exception $e) {
            vedmg_log_warning('WOOCOMMERCE', "Query failed on Vedmg-woo-LMS table: " . $e->getMessage());
        }

        return null;
    }
    
    /**
     * Enroll student in course
     */
    private static function enroll_student_in_course($customer_data, $course_purchase, $order_status, $order_id = null) {
        global $wpdb;
        
        // Get course information
        $course_info = self::get_course_info($course_purchase['course_id']);
        if (!$course_info) {
            vedmg_log_warning('WOOCOMMERCE', 'Course not found in VedMG database: ' . $course_purchase['course_id']);
            return;
        }
        
        // Prepare enrollment data
        $enrollment_data = array(
            'student_id' => $customer_data['user_id'],
            'student_name' => $customer_data['full_name'],
            'student_email' => $customer_data['email'],
            'student_phone' => $customer_data['phone'],
            'course_id' => $course_info->course_id,
            'woocommerce_order_id' => $order_id,
            'google_classroom_id' => $course_info->google_classroom_id,
            'enrollment_status' => ($order_status === 'completed') ? 'enrolled' : 'pending',
            'purchase_date' => current_time('mysql'),
            'enrollment_date' => current_time('mysql'),
            'created_date' => current_time('mysql'),
            'updated_date' => current_time('mysql')
        );
        
        // Insert or update enrollment
        $existing_enrollment = self::get_existing_enrollment($customer_data['user_id'], $course_info->course_id);
        
        if ($existing_enrollment) {
            // Update existing enrollment
            $wpdb->update(
                $wpdb->prefix . 'vedmg_student_enrollments',
                $enrollment_data,
                array('enrollment_id' => $existing_enrollment->enrollment_id),
                array('%d', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s'),
                array('%d')
            );
            
            vedmg_log_info('WOOCOMMERCE', 'Updated enrollment for student: ' . $customer_data['email']);
        } else {
            // Create new enrollment
            $wpdb->insert(
                $wpdb->prefix . 'vedmg_student_enrollments',
                $enrollment_data,
                array('%d', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
            );
            
            vedmg_log_info('WOOCOMMERCE', 'Created new enrollment for student: ' . $customer_data['email']);
        }
        
        // Send enrollment notification and share calendar if order is completed
        if ($order_status === 'completed' && !empty($course_info->google_classroom_id)) {
            self::send_enrollment_notification($customer_data, $course_info);

            // Share calendar if calendar_id exists
            if (!empty($course_info->calendar_id)) {
                // Share the calendar with student
                $calendar_shared = self::share_calendar_with_student($customer_data, $course_info);

                // TODO: Calendar invite functionality disabled for now
                // Will be enabled when scheduling specific meetings
                /*
                // Send calendar invite after sharing calendar
                if ($calendar_shared) {
                    self::send_calendar_invite_to_student($customer_data, $course_info);
                }
                */
            }
        }
    }
    
    /**
     * Get course information from VedMG database
     * Enhanced to provide detailed logging for calendar sharing decisions
     */
    private static function get_course_info($masterstudy_course_id) {
        global $wpdb;

        $course_info = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}vedmg_courses
            WHERE masterstudy_course_id = %d
        ", $masterstudy_course_id));

        if ($course_info) {
            vedmg_log_info('WOOCOMMERCE', "Found course in VedMG database: {$course_info->course_name} (ID: {$course_info->course_id})");
            vedmg_log_info('WOOCOMMERCE', "Course details - Google Classroom ID: " . ($course_info->google_classroom_id ?: 'none') . ", Calendar ID: " . ($course_info->calendar_id ?: 'none'));

            // Log calendar sharing eligibility
            if (!empty($course_info->calendar_id)) {
                vedmg_log_info('WOOCOMMERCE', "✅ Course has calendar_id - calendar sharing will be attempted");
            } else {
                vedmg_log_info('WOOCOMMERCE', "❌ Course has no calendar_id - calendar sharing will be skipped");
            }
        } else {
            vedmg_log_warning('WOOCOMMERCE', "Course not found in VedMG database for MasterStudy course ID: $masterstudy_course_id");
        }

        return $course_info;
    }
    
    /**
     * Get existing enrollment
     */
    private static function get_existing_enrollment($student_id, $course_id) {
        global $wpdb;
        
        return $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}vedmg_student_enrollments 
            WHERE student_id = %d AND course_id = %d
        ", $student_id, $course_id));
    }
    
    /**
     * Send enrollment notification to student
     */
    private static function send_enrollment_notification($customer_data, $course_info) {
        // Prepare email data
        $subject = 'Welcome to ' . $course_info->course_name . ' - Google Classroom Access';
        
        $message = "Dear {$customer_data['full_name']},\n\n";
        $message .= "Congratulations! You have been successfully enrolled in:\n";
        $message .= "Course: {$course_info->course_name}\n\n";
        
        if (!empty($course_info->class_join_link)) {
            $message .= "Google Classroom Link: {$course_info->class_join_link}\n";
        }
        
        if (!empty($course_info->instructor_name)) {
            $message .= "Instructor: {$course_info->instructor_name}\n";
        }
        
        $message .= "\nPlease join the Google Classroom using the link above to access course materials and participate in discussions.\n\n";
        $message .= "If you have any questions, please contact our support team.\n\n";
        $message .= "Best regards,\nVedMG ClassRoom Team";
        
        // Send email
        $sent = wp_mail($customer_data['email'], $subject, $message);
        
        if ($sent) {
            vedmg_log_info('EMAIL', 'Enrollment notification sent to: ' . $customer_data['email']);
        } else {
            vedmg_log_error('EMAIL', 'Failed to send enrollment notification to: ' . $customer_data['email']);
        }
        
        return $sent;
    }

    /**
     * Share calendar with student after enrollment
     */
    private static function share_calendar_with_student($customer_data, $course_info) {
        vedmg_log_info('WOOCOMMERCE', 'Sharing calendar with student: ' . $customer_data['email']);

        try {
            // Prepare calendar sharing API data
            $api_data = array(
                'calendar_id' => $course_info->calendar_id,
                'user_email' => $customer_data['email'],
                'instructor_email' => $course_info->instructor_email
            );

            $api_url = 'https://gclassroom-839391304260.us-central1.run.app/share_calender';
            $api_key = 'G$$gle@VedMG!@#';

            vedmg_log_info('WOOCOMMERCE', 'Making calendar sharing API call', array(
                'calendar_id' => $course_info->calendar_id,
                'student_email' => $customer_data['email'],
                'instructor_email' => $course_info->instructor_email
            ));

            // Make API call
            $response = wp_remote_post($api_url, array(
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'x-api-key' => $api_key
                ),
                'body' => json_encode($api_data),
                'timeout' => 30
            ));

            if (is_wp_error($response)) {
                vedmg_log_error('WOOCOMMERCE', 'Calendar sharing API call failed', $response->get_error_message());
                return false;
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code === 200) {
                $response_data = json_decode($response_body, true);
                if ($response_data && isset($response_data['message'])) {
                    vedmg_log_info('WOOCOMMERCE', 'Calendar shared successfully', array(
                        'student_email' => $customer_data['email'],
                        'course_name' => $course_info->course_name,
                        'api_response' => $response_data['message']
                    ));
                    return true;
                } else {
                    vedmg_log_info('WOOCOMMERCE', 'Calendar sharing API call successful', array(
                        'response_body' => $response_body
                    ));
                    return true;
                }
            } else if ($response_code === 500) {
                vedmg_log_error('WOOCOMMERCE', 'Calendar sharing API server error (500)', array(
                    'student_email' => $customer_data['email'],
                    'course_name' => $course_info->course_name,
                    'calendar_id' => $course_info->calendar_id,
                    'note' => 'API server is experiencing issues - will retry later'
                ));
                // Return true to continue with invite API even if sharing fails
                return true;
            } else {
                vedmg_log_error('WOOCOMMERCE', 'Calendar sharing failed', array(
                    'response_code' => $response_code,
                    'response_body' => $response_body,
                    'student_email' => $customer_data['email'],
                    'course_name' => $course_info->course_name
                ));
                return false;
            }

        } catch (Exception $e) {
            vedmg_log_error('WOOCOMMERCE', 'Calendar sharing exception', array(
                'error' => $e->getMessage(),
                'student_email' => $customer_data['email'],
                'course_name' => $course_info->course_name
            ));
            return false;
        }
    }

    /**
     * Send calendar invite to student after enrollment
     *
     * NOTE: This function is currently disabled in the enrollment flow
     * It will be used later for scheduling specific meetings/sessions
     * The function remains available for future use
     */
    private static function send_calendar_invite_to_student($customer_data, $course_info) {
        vedmg_log_info('WOOCOMMERCE', 'Sending calendar invite to student: ' . $customer_data['email']);

        try {
            // Calculate meeting times (next Monday at 2 PM IST for 1 hour)
            $next_monday = date('Y-m-d\T14:00:00+05:30', strtotime('next monday'));
            $end_time = date('Y-m-d\T15:00:00+05:30', strtotime('next monday +1 hour'));

            // Prepare calendar invite API data
            $invite_data = array(
                'calendar_id' => $course_info->calendar_id,
                'user_email' => $customer_data['email'],
                'summary' => "Welcome to {$course_info->course_name} - Orientation Session",
                'location' => 'Online via Google Meet',
                'description' => "Welcome to {$course_info->course_name}!\n\n" .
                               "This is your course orientation session where we will:\n" .
                               "• Introduce the course structure and objectives\n" .
                               "• Explain how to access course materials\n" .
                               "• Answer any questions you may have\n" .
                               "• Set up your learning schedule\n\n" .
                               "Please join on time. Looking forward to seeing you!\n\n" .
                               "Best regards,\n{$course_info->instructor_name}",
                'start_datetime' => $next_monday,
                'end_datetime' => $end_time,
                'instructor_email' => $course_info->instructor_email,
                'meeting_frequency' => 'FREQ=WEEKLY;BYDAY=MO;COUNT=4' // Weekly on Monday for 4 weeks
            );

            $api_url = 'https://gclassroom-839391304260.us-central1.run.app/share_invite';
            $api_key = 'G$$gle@VedMG!@#';

            vedmg_log_info('WOOCOMMERCE', 'Making calendar invite API call', array(
                'calendar_id' => $course_info->calendar_id,
                'student_email' => $customer_data['email'],
                'instructor_email' => $course_info->instructor_email,
                'meeting_time' => $next_monday
            ));

            // Make API call
            $response = wp_remote_post($api_url, array(
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'x-api-key' => $api_key
                ),
                'body' => json_encode($invite_data),
                'timeout' => 30
            ));

            if (is_wp_error($response)) {
                vedmg_log_error('WOOCOMMERCE', 'Calendar invite API call failed', $response->get_error_message());
                return false;
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code === 200) {
                $response_data = json_decode($response_body, true);
                if ($response_data && isset($response_data['id'])) {
                    vedmg_log_info('WOOCOMMERCE', 'Calendar invite sent successfully', array(
                        'student_email' => $customer_data['email'],
                        'course_name' => $course_info->course_name,
                        'event_id' => $response_data['id'],
                        'event_status' => $response_data['status'] ?? 'unknown'
                    ));
                    return true;
                } else {
                    vedmg_log_info('WOOCOMMERCE', 'Calendar invite API call successful', array(
                        'response_body' => $response_body
                    ));
                    return true;
                }
            } else if ($response_code === 500) {
                vedmg_log_error('WOOCOMMERCE', 'Calendar invite API server error (500)', array(
                    'student_email' => $customer_data['email'],
                    'course_name' => $course_info->course_name,
                    'calendar_id' => $course_info->calendar_id,
                    'note' => 'API server is experiencing issues - student will not receive calendar invite'
                ));
                return false;
            } else {
                vedmg_log_error('WOOCOMMERCE', 'Calendar invite failed', array(
                    'response_code' => $response_code,
                    'response_body' => $response_body,
                    'student_email' => $customer_data['email'],
                    'course_name' => $course_info->course_name
                ));
                return false;
            }

        } catch (Exception $e) {
            vedmg_log_error('WOOCOMMERCE', 'Calendar invite exception', array(
                'error' => $e->getMessage(),
                'student_email' => $customer_data['email'],
                'course_name' => $course_info->course_name
            ));
            return false;
        }
    }
    
    /**
     * Manual enrollment function (for admin use)
     */
    public static function manual_enroll_student($student_id, $course_id, $send_notification = true) {
        $user = get_userdata($student_id);
        if (!$user) {
            throw new Exception('Student not found');
        }
        
        $course_info = self::get_course_info_by_id($course_id);
        if (!$course_info) {
            throw new Exception('Course not found');
        }
        
        $customer_data = array(
            'user_id' => $user->ID,
            'full_name' => $user->display_name,
            'email' => $user->user_email,
            'phone' => get_user_meta($user->ID, 'billing_phone', true)
        );
        
        $course_purchase = array(
            'course_id' => $course_info->masterstudy_course_id,
            'total' => 0
        );
        
        self::enroll_student_in_course($customer_data, $course_purchase, 'completed');

        if ($send_notification) {
            self::send_enrollment_notification($customer_data, $course_info);
        }

        // Share calendar if calendar_id exists
        if (!empty($course_info->calendar_id)) {
            // Share the calendar with student
            $calendar_shared = self::share_calendar_with_student($customer_data, $course_info);

            // TODO: Calendar invite functionality disabled for now
            // Will be enabled when scheduling specific meetings
            /*
            // Send calendar invite after sharing calendar
            if ($calendar_shared) {
                self::send_calendar_invite_to_student($customer_data, $course_info);
            }
            */
        }

        return true;
    }
    
    /**
     * Get course info by course ID
     */
    private static function get_course_info_by_id($course_id) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}vedmg_courses
            WHERE course_id = %d
        ", $course_id));
    }


    
    /**
     * Manual sync all WooCommerce orders
     * Called when sync button is clicked
     */
    public static function manual_sync_all() {
        global $wpdb;
        
        vedmg_log_info('WOOCOMMERCE', '=== MANUAL SYNC STARTED ===');
        vedmg_log_info('WOOCOMMERCE', 'Checking WooCommerce integration status...');
        
        try {
            // 1. Check if WooCommerce is active
            vedmg_log_info('WOOCOMMERCE', 'Step 1: Checking if WooCommerce is active');
            if (!self::is_woocommerce_active()) {
                vedmg_log_error('WOOCOMMERCE', 'WooCommerce plugin is not active');
                return array(
                    'success' => false,
                    'message' => 'WooCommerce plugin is not active'
                );
            }
            vedmg_log_info('WOOCOMMERCE', '✓ WooCommerce is active');
            
            // 2. Ensure our custom tables exist
            vedmg_log_info('WOOCOMMERCE', 'Step 2: Checking custom database tables');
            require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/activator.php';
            if (!VedMG_ClassRoom_Database_Activator::verify_tables_exist()) {
                vedmg_log_warning('WOOCOMMERCE', 'Custom tables missing - creating them now');
                // Create tables if they don't exist
                VedMG_ClassRoom_Database_Activator::activate();
                vedmg_log_info('WOOCOMMERCE', '✓ Created missing custom tables during sync');
            } else {
                vedmg_log_info('WOOCOMMERCE', '✓ Custom tables exist');
            }
            
            // 3. First check if we have any courses in our database
            vedmg_log_info('WOOCOMMERCE', 'Step 3: Checking for courses in vedmg_courses table');
            $course_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_courses");
            vedmg_log_info('WOOCOMMERCE', "Found $course_count courses in vedmg_courses table");
            
            if ($course_count == 0) {
                vedmg_log_error('WOOCOMMERCE', 'No courses found in VedMG database');
                return array(
                    'success' => false,
                    'message' => 'No courses found in VedMG database. Please sync MasterStudy courses first.'
                );
            }
            vedmg_log_info('WOOCOMMERCE', '✓ Courses available for enrollment matching');
            
            // 4. Check which WooCommerce table structure we have (HPOS or legacy)
            vedmg_log_info('WOOCOMMERCE', 'Step 4: Detecting WooCommerce database structure');
            $hpos_orders_table = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}wc_orders'");
            $hpos_items_table = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}wc_order_items'");
            
            $legacy_posts_table = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->posts}'");
            $legacy_items_table = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}woocommerce_order_items'");
            
            vedmg_log_info('WOOCOMMERCE', 'HPOS Orders Table: ' . ($hpos_orders_table ? 'EXISTS' : 'NOT FOUND'));
            vedmg_log_info('WOOCOMMERCE', 'HPOS Items Table: ' . ($hpos_items_table ? 'EXISTS' : 'NOT FOUND'));
            vedmg_log_info('WOOCOMMERCE', 'Legacy Posts Table: ' . ($legacy_posts_table ? 'EXISTS' : 'NOT FOUND'));
            vedmg_log_info('WOOCOMMERCE', 'Legacy Items Table: ' . ($legacy_items_table ? 'EXISTS' : 'NOT FOUND'));
            
            if (!$hpos_orders_table && !$legacy_posts_table) {
                vedmg_log_error('WOOCOMMERCE', 'No WooCommerce order tables found');
                return array(
                    'success' => false,
                    'message' => 'WooCommerce order tables not found in database'
                );
            }
            
            if (!$hpos_items_table && !$legacy_items_table) {
                vedmg_log_error('WOOCOMMERCE', 'No WooCommerce order items tables found');
                return array(
                    'success' => false,
                    'message' => 'WooCommerce order items tables not found in database'
                );
            }
            
            $synced_count = 0;
            $updated_count = 0;
            $new_count = 0;
            $processed_enrollments = 0;
            
            // 5. Get WooCommerce orders - SIMPLE APPROACH: Match by item name
            vedmg_log_info('WOOCOMMERCE', 'Step 5: Getting ALL WooCommerce orders (simple name matching)');
            
            // Simple query - just get all order items with customer info
            vedmg_log_info('WOOCOMMERCE', 'Using simple approach: matching item names with course names');
            $sql = "
                SELECT DISTINCT
                    p.ID as order_id,
                    pm_customer.meta_value as user_id,
                    pm_email.meta_value as billing_email,
                    pm_first.meta_value as billing_first_name,
                    pm_last.meta_value as billing_last_name,
                    pm_phone.meta_value as billing_phone,
                    p.post_date as order_date,
                    p.post_status as status,
                    oi.order_item_id as item_id,
                    oi.order_item_name as item_name
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm_customer ON p.ID = pm_customer.post_id AND pm_customer.meta_key = '_customer_user'
                LEFT JOIN {$wpdb->postmeta} pm_email ON p.ID = pm_email.post_id AND pm_email.meta_key = '_billing_email'
                LEFT JOIN {$wpdb->postmeta} pm_first ON p.ID = pm_first.post_id AND pm_first.meta_key = '_billing_first_name'
                LEFT JOIN {$wpdb->postmeta} pm_last ON p.ID = pm_last.post_id AND pm_last.meta_key = '_billing_last_name'
                LEFT JOIN {$wpdb->postmeta} pm_phone ON p.ID = pm_phone.post_id AND pm_phone.meta_key = '_billing_phone'
                JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
                WHERE p.post_type = 'shop_order'
                AND p.post_status IN ('wc-completed', 'wc-processing')
                AND oi.order_item_name IS NOT NULL
                AND oi.order_item_name != ''
                ORDER BY p.post_date DESC
                LIMIT 1000
            ";
            vedmg_log_info('WOOCOMMERCE', 'Simple Query: ' . $sql);
            $order_data = $wpdb->get_results($sql);
            
            $order_count = count($order_data);
            vedmg_log_info('WOOCOMMERCE', "Query completed - Found $order_count course orders");
            
            if (empty($order_data)) {
                vedmg_log_warning('WOOCOMMERCE', 'No course orders found in WooCommerce database');
                return array(
                    'success' => true,
                    'message' => 'No course orders found in WooCommerce',
                    'synced_count' => 0,
                    'updated_count' => 0,
                    'new_count' => 0,
                    'enrollments_count' => 0
                );
            }
            
            // 6. Process each order item - SIMPLE NAME MATCHING
            vedmg_log_info('WOOCOMMERCE', 'Step 6: Processing order items with simple name matching');
            
            foreach ($order_data as $index => $order_item) {
                $item_num = $index + 1;
                vedmg_log_info('WOOCOMMERCE', "Processing item $item_num of $order_count: Order {$order_item->order_id}, Item: '{$order_item->item_name}'");
                
                // SIMPLE MATCHING: Find course by matching item name with course name
                vedmg_log_info('WOOCOMMERCE', "Looking for course with name matching: '{$order_item->item_name}'");
                $course_info = $wpdb->get_row($wpdb->prepare("
                    SELECT course_id, course_name, instructor_name, google_classroom_id
                    FROM {$wpdb->prefix}vedmg_courses 
                    WHERE course_name = %s
                ", $order_item->item_name));
                
                if (!$course_info) {
                    vedmg_log_warning('WOOCOMMERCE', "❌ No course found with name '{$order_item->item_name}'. Skipping.");
                    continue;
                }
                
                vedmg_log_info('WOOCOMMERCE', "✅ MATCH FOUND! Course: {$course_info->course_name} (ID: {$course_info->course_id})");
                
                // Check if enrollment already exists (check by student_id AND course_id to avoid duplicates)
                vedmg_log_info('WOOCOMMERCE', "Checking for existing enrollment: Student {$order_item->user_id}, Course {$course_info->course_id}");
                $existing_enrollment = $wpdb->get_row($wpdb->prepare("
                    SELECT enrollment_id, google_classroom_id FROM {$wpdb->prefix}vedmg_student_enrollments
                    WHERE student_id = %d AND course_id = %d
                ", $order_item->user_id ?: 0, $course_info->course_id));
                
                // Prepare enrollment data with fallback for student name
                $student_name = trim(($order_item->billing_first_name ?: '') . ' ' . ($order_item->billing_last_name ?: ''));

                // If billing name is empty, try to get WordPress user display name
                if (empty($student_name) && !empty($order_item->user_id)) {
                    $user = get_userdata($order_item->user_id);
                    if ($user) {
                        $student_name = $user->display_name ?: $user->user_login;
                    }
                }

                // Final fallback to email prefix if still empty
                if (empty($student_name) && !empty($order_item->billing_email)) {
                    $email_parts = explode('@', $order_item->billing_email);
                    $student_name = ucfirst($email_parts[0]);
                }

                $enrollment_status = ($order_item->status === 'wc-completed') ? 'enrolled' : 'pending';
                
                vedmg_log_info('WOOCOMMERCE', "Student: {$student_name} ({$order_item->billing_email})");
                vedmg_log_info('WOOCOMMERCE', "Status: {$enrollment_status}");
                
                // Prepare enrollment data with proper Google Classroom ID handling
                $google_classroom_id = $course_info->google_classroom_id;

                // If course doesn't have a Google Classroom ID but existing enrollment does, preserve it
                if (empty($google_classroom_id) && $existing_enrollment && !empty($existing_enrollment->google_classroom_id)) {
                    $google_classroom_id = $existing_enrollment->google_classroom_id;
                    vedmg_log_info('WOOCOMMERCE', "Preserving existing Google Classroom ID: {$google_classroom_id}");
                }

                $enrollment_data = array(
                    'student_id' => $order_item->user_id ?: 0,
                    'student_name' => $student_name,
                    'student_email' => $order_item->billing_email ?: '',
                    'student_phone' => $order_item->billing_phone ?: '',
                    'course_id' => $course_info->course_id,
                    'woocommerce_order_id' => $order_item->order_id,
                    'google_classroom_id' => $google_classroom_id,
                    'enrollment_status' => $enrollment_status,
                    'purchase_date' => $order_item->order_date,
                    'updated_date' => date('Y-m-d H:i:s')
                );
                
                if ($existing_enrollment) {
                    // Update existing enrollment
                    vedmg_log_info('WOOCOMMERCE', "Updating existing enrollment ID: {$existing_enrollment->enrollment_id}");
                    $result = $wpdb->update(
                        $wpdb->prefix . 'vedmg_student_enrollments',
                        $enrollment_data,
                        array('enrollment_id' => $existing_enrollment->enrollment_id),
                        array('%d', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s'),
                        array('%d')
                    );
                    
                    if ($result !== false) {
                        $updated_count++;
                        vedmg_log_info('WOOCOMMERCE', "✅ Updated enrollment: {$course_info->course_name}");
                    }
                } else {
                    // Create new enrollment
                    vedmg_log_info('WOOCOMMERCE', "Creating new enrollment");
                    $enrollment_data['created_date'] = date('Y-m-d H:i:s');
                    if ($enrollment_data['enrollment_status'] === 'enrolled') {
                        $enrollment_data['enrollment_date'] = date('Y-m-d H:i:s');
                    }
                    
                    $result = $wpdb->insert(
                        $wpdb->prefix . 'vedmg_student_enrollments',
                        $enrollment_data,
                        array('%d', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
                    );
                    
                    if ($result !== false) {
                        $new_enrollment_id = $wpdb->insert_id;
                        $new_count++;
                        vedmg_log_info('WOOCOMMERCE', "✅ Created enrollment ID {$new_enrollment_id}: {$course_info->course_name}");

                        // Share calendar if enrollment is completed and calendar ID is available
                        if ($enrollment_data['enrollment_status'] === 'enrolled' && !empty($course_info->calendar_id)) {
                            $customer_data = array('email' => $order_item->billing_email);
                            self::share_calendar_with_student($customer_data, $course_info);
                        }
                    }
                }

                // For any enrollment (new or updated), check if we need to share calendar
                if ($enrollment_data['enrollment_status'] === 'enrolled' && !empty($course_info->calendar_id)) {
                    $customer_data = array('email' => $order_item->billing_email);
                    self::share_calendar_with_student($customer_data, $course_info);
                }

                $processed_enrollments++;
                $synced_count++;
                vedmg_log_info('WOOCOMMERCE', "--- Item $item_num completed ---");
            }
            
            // 7. Update last sync time
            vedmg_log_info('WOOCOMMERCE', 'Step 7: Updating last sync time');
            if (function_exists('update_option')) {
                update_option('vedmg_classroom_last_sync_woocommerce', time());
            }
            
            vedmg_log_info('WOOCOMMERCE', "=== MANUAL SYNC COMPLETED ===");
            vedmg_log_info('WOOCOMMERCE', "FINAL STATS: $synced_count course orders processed, $processed_enrollments enrollments ($new_count new, $updated_count updated)");
            
            $success_message = "Successfully synced $synced_count course orders from WooCommerce with $processed_enrollments enrollments ($new_count new, $updated_count updated)";
            vedmg_log_info('WOOCOMMERCE', "SUCCESS: " . $success_message);
            
            return array(
                'success' => true,
                'message' => $success_message,
                'synced_count' => $synced_count,
                'updated_count' => $updated_count,
                'new_count' => $new_count,
                'enrollments_count' => $processed_enrollments
            );
            
        } catch (Exception $e) {
            vedmg_log_error('WOOCOMMERCE', '=== MANUAL SYNC FAILED ===');
            vedmg_log_error('WOOCOMMERCE', 'Exception: ' . $e->getMessage());
            vedmg_log_error('WOOCOMMERCE', 'Stack trace: ' . $e->getTraceAsString());
            
            return array(
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Check if WooCommerce is active
     */
    public static function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }
    
    /**
     * Diagnostic function to check WooCommerce data
     */
    public static function diagnose_woocommerce_data() {
        global $wpdb;
        
        vedmg_log_info('WOOCOMMERCE', '=== DIAGNOSTIC: WooCommerce Data Analysis ===');
        
        // Check WooCommerce tables
        $tables_to_check = array(
            'wc_orders' => $wpdb->prefix . 'wc_orders',
            'wc_order_items' => $wpdb->prefix . 'wc_order_items', 
            'wc_order_itemmeta' => $wpdb->prefix . 'wc_order_itemmeta',
            'posts' => $wpdb->posts,
            'woocommerce_order_items' => $wpdb->prefix . 'woocommerce_order_items',
            'woocommerce_order_itemmeta' => $wpdb->prefix . 'woocommerce_order_itemmeta'
        );
        
        foreach ($tables_to_check as $name => $table) {
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'");
            vedmg_log_info('WOOCOMMERCE', "Table $name ($table): " . ($exists ? 'EXISTS' : 'NOT FOUND'));
            
            if ($exists) {
                $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
                vedmg_log_info('WOOCOMMERCE', "Table $name has $count records");
            }
        }
        
        // Check for courses in WooCommerce orders
        $course_orders = $wpdb->get_var("
            SELECT COUNT(DISTINCT oim.order_item_id)
            FROM {$wpdb->prefix}woocommerce_order_items oi
            LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
            WHERE oim.meta_key = '_stm_lms_course_id'
            AND oim.meta_value IS NOT NULL
            AND oim.meta_value != ''
        ");
        
        vedmg_log_info('WOOCOMMERCE', "Found $course_orders order items with course IDs");
        
        // Check for sample order item names
        vedmg_log_info('WOOCOMMERCE', '=== SAMPLE ORDER ITEM NAMES ===');
        $sample_items = $wpdb->get_results("
            SELECT 
                p.ID as order_id,
                p.post_status,
                p.post_date,
                oi.order_item_name as item_name
            FROM {$wpdb->posts} p
            JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
            WHERE p.post_type = 'shop_order'
            AND p.post_status IN ('wc-completed', 'wc-processing')
            AND oi.order_item_name IS NOT NULL
            AND oi.order_item_name != ''
            ORDER BY p.post_date DESC
            LIMIT 10
        ");
        
        vedmg_log_info('WOOCOMMERCE', 'Sample order items found: ' . count($sample_items));
        foreach ($sample_items as $item) {
            vedmg_log_info('WOOCOMMERCE', "Order {$item->order_id}: Status={$item->post_status}, Item='{$item->item_name}'");
        }
        
        // Check if any item names match course names
        vedmg_log_info('WOOCOMMERCE', '=== CHECKING FOR COURSE NAME MATCHES ===');
        $course_matches = $wpdb->get_results("
            SELECT 
                c.course_name,
                COUNT(oi.order_item_id) as order_count
            FROM {$wpdb->prefix}vedmg_courses c
            LEFT JOIN {$wpdb->prefix}woocommerce_order_items oi ON c.course_name = oi.order_item_name
            LEFT JOIN {$wpdb->posts} p ON oi.order_id = p.ID AND p.post_type = 'shop_order' AND p.post_status IN ('wc-completed', 'wc-processing')
            GROUP BY c.course_name
            HAVING order_count > 0
            ORDER BY order_count DESC
        ");
        
        vedmg_log_info('WOOCOMMERCE', 'Course name matches found: ' . count($course_matches));
        foreach ($course_matches as $match) {
            vedmg_log_info('WOOCOMMERCE', "Course '{$match->course_name}': {$match->order_count} orders");
        }
        
        // NEW: Check what course-related meta keys actually exist
        vedmg_log_info('WOOCOMMERCE', '=== SEARCHING FOR COURSE-RELATED METADATA ===');
        $course_meta_keys_query = "
            SELECT DISTINCT meta_key, COUNT(*) as usage_count
            FROM {$wpdb->prefix}woocommerce_order_itemmeta 
            WHERE meta_key LIKE '%course%' 
               OR meta_key LIKE '%stm%'
               OR meta_key LIKE '%lms%'
               OR meta_key LIKE '%class%'
               OR meta_key LIKE '%lesson%'
            GROUP BY meta_key
            ORDER BY usage_count DESC
            LIMIT 20
        ";
        $course_meta_keys = $wpdb->get_results($course_meta_keys_query);
        
        if (empty($course_meta_keys)) {
            vedmg_log_info('WOOCOMMERCE', 'No course-related meta keys found');
        } else {
            vedmg_log_info('WOOCOMMERCE', 'Found ' . count($course_meta_keys) . ' course-related meta keys:');
            foreach ($course_meta_keys as $meta) {
                vedmg_log_info('WOOCOMMERCE', "  - {$meta->meta_key}: {$meta->usage_count} uses");
            }
        }
        
        // Check all meta keys with significant usage
        vedmg_log_info('WOOCOMMERCE', '=== ALL META KEYS WITH 5+ USES ===');
        $all_meta_keys_query = "
            SELECT meta_key, COUNT(*) as usage_count
            FROM {$wpdb->prefix}woocommerce_order_itemmeta 
            GROUP BY meta_key
            HAVING COUNT(*) >= 5
            ORDER BY usage_count DESC
            LIMIT 30
        ";
        $all_meta_keys = $wpdb->get_results($all_meta_keys_query);
        
        foreach ($all_meta_keys as $meta) {
            vedmg_log_info('WOOCOMMERCE', "  - {$meta->meta_key}: {$meta->usage_count} uses");
        }
        
        vedmg_log_info('WOOCOMMERCE', '=== END DIAGNOSTIC ===');
        
        return array(
            'success' => true,
            'message' => 'Diagnostic completed - check debug.log for details',
            'course_orders' => count($course_matches)
        );
    }
}

// Initialize WooCommerce integration
VedMG_ClassRoom_WooCommerce_Integration::init();
