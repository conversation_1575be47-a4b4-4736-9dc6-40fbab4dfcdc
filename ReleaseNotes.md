# 🎓 VedMG ClassRoom Plugin - Comprehensive Documentation

**Version:** 2.1.0  
**Last Updated:** August 2025  
**WordPress Compatibility:** 5.0+  
**PHP Version:** 7.4+

---

## 📋 Table of Contents

1. [Plugin Overview](#plugin-overview)
2. [Key Features](#key-features)
3. [Technical Architecture](#technical-architecture)
4. [API Integrations](#api-integrations)
5. [Database Schema](#database-schema)
6. [Installation & Setup](#installation--setup)
7. [Usage Guide](#usage-guide)
8. [Recent Updates](#recent-updates)
9. [Known Issues](#known-issues)
10. [Developer Notes](#developer-notes)

---

## 🎯 Plugin Overview

The **VedMG ClassRoom Plugin** is a comprehensive WordPress solution that seamlessly integrates Google Classroom, MasterStudy LMS, and WooCommerce to create a unified educational platform. It enables automatic synchronization of courses, students, and instructors across multiple platforms while providing advanced calendar management and enrollment tracking capabilities.

### **Core Purpose**
- **Unified Course Management**: Centralize courses from Google Classroom, MasterStudy LMS, and custom WordPress courses
- **Automated Student Enrollment**: Streamline student registration and enrollment across platforms
- **Calendar Integration**: Automatic calendar sharing and meeting scheduling for students
- **E-commerce Integration**: Connect course sales through WooCommerce with automatic enrollment
- **Instructor Management**: Comprehensive instructor sync and classroom management tools

---

## ✨ Key Features

### **🔄 Google Classroom Integration**
- **Bi-directional Sync**: Automatic synchronization between Google Classroom and WordPress
- **Smart Course Matching**: Advanced algorithm to match courses across platforms using keyword analysis
- **Student Auto-Enrollment**: Automatic enrollment of Google Classroom students into WordPress
- **Real-time Status Updates**: Live sync status monitoring with detailed progress feedback
- **Instructor Authentication**: Secure Google OAuth integration for instructor access

### **📅 Advanced Calendar Management**
- **Automatic Calendar Creation**: Creates Google Calendars for new courses when not provided
- **Calendar Sharing**: Automatically shares course calendars with enrolled students
- **Meeting Invitations**: Sends personalized calendar invites with course orientation sessions
- **Recurring Events**: Supports weekly recurring meetings and sessions
- **Fallback API Support**: Uses backup calendar creation API when primary fails

### **👥 Student Enrollment System**
- **Multi-Platform Tracking**: Tracks enrollments across Google Classroom, MasterStudy, and WooCommerce
- **Automated User Creation**: Creates WordPress users for Google Classroom students
- **Progress Monitoring**: Tracks student progress and completion status
- **Enrollment Analytics**: Comprehensive reporting on enrollment statistics
- **Bulk Operations**: Support for bulk enrollment and management operations

### **🛒 WooCommerce Integration**
- **Automatic Enrollment**: Students automatically enrolled upon course purchase
- **Order Tracking**: Links enrollments to WooCommerce orders for complete audit trail
- **Calendar Integration**: Purchased courses automatically trigger calendar sharing
- **Payment Verification**: Ensures enrollment only after successful payment
- **Refund Handling**: Manages enrollment status changes for refunds

### **🎓 MasterStudy LMS Integration**
- **Course Synchronization**: Syncs courses between MasterStudy and VedMG systems
- **Enrollment Mapping**: Maps MasterStudy enrollments to VedMG tracking
- **Progress Sync**: Synchronizes student progress across platforms
- **Certificate Integration**: Links course completion certificates

### **👨‍🏫 Instructor Management**
- **Instructor Sync Dashboard**: Dedicated interface for instructor management
- **Google Authentication**: Secure OAuth flow for Google Classroom access
- **Sync Status Monitoring**: Real-time tracking of sync operations
- **Error Recovery**: Automatic retry mechanisms for failed operations
- **Bulk Instructor Operations**: Mass sync and management capabilities

### **📊 Admin Panel Features**
- **Comprehensive Dashboard**: Overview of all courses, students, and sync status
- **Detailed Reporting**: Analytics on enrollments, sync operations, and course performance
- **Error Monitoring**: Centralized error tracking and resolution tools
- **Debug Logging**: Detailed logging system for troubleshooting
- **Configuration Management**: Easy setup and configuration options

---

## 🏗️ Technical Architecture

### **Plugin Structure**
```
VedMG-ClassRoom/
├── VedMG-ClassRoom.php          # Main plugin file
├── class-core.php               # Core plugin functionality
├── admin/                       # Admin interface components
│   ├── admin.php               # Admin functionality and AJAX handlers
│   ├── pages/                  # Admin page templates
│   │   ├── instructor_sync.php # Instructor synchronization interface
│   │   ├── courses.php         # Course management interface
│   │   └── enrollments.php     # Student enrollment management
│   ├── js/                     # JavaScript files
│   │   ├── instructors.js      # Instructor sync functionality
│   │   └── courses.js          # Course management scripts
│   └── css/                    # Stylesheet files
├── integrations/               # External platform integrations
│   ├── woocommerce.php        # WooCommerce integration
│   ├── masterstudy.php        # MasterStudy LMS integration
│   └── google_classroom.php   # Google Classroom API integration
├── api/                       # API endpoint handlers
│   ├── courses.php           # Course API endpoints
│   └── students.php          # Student API endpoints
├── database/                  # Database management
│   ├── activator.php         # Database table creation
│   └── schema.sql            # Database schema definitions
└── Debug/                    # Debugging and logging
    └── debugLog.php          # Debug logging functionality
```

### **Core Components**

#### **1. VedMG_ClassRoom_Core**
- **Purpose**: Central plugin management and initialization
- **Responsibilities**: Plugin activation, deactivation, database setup, hook registration
- **Location**: `class-core.php`

#### **2. Admin Interface System**
- **Purpose**: WordPress admin panel integration
- **Components**: Dashboard pages, AJAX handlers, user interface elements
- **Location**: `admin/` directory

#### **3. Integration Layer**
- **Purpose**: External platform connectivity
- **Components**: Google Classroom API, WooCommerce hooks, MasterStudy sync
- **Location**: `integrations/` directory

#### **4. Database Layer**
- **Purpose**: Data persistence and management
- **Components**: Custom tables, data models, migration scripts
- **Location**: `database/` directory

---

## 🔌 API Integrations

### **Google Classroom APIs**

#### **Primary APIs**
```php
// Course Creation API
POST https://gclassroom-839391304260.us-central1.run.app/create_class
Headers: x-api-key: G$$gle@VedMG!@#
Body: {
    "name": "Course Name",
    "section": "Section Name",
    "descriptionHeading": "Course Description",
    "room": "Online",
    "instructor_email": "<EMAIL>"
}
```

#### **Calendar Management APIs**
```php
// Calendar Creation API (Fallback)
POST https://gclassroom-839391304260.us-central1.run.app/create_class_calendar
Headers: x-api-key: G$$gle@VedMG!@#
Body: {
    "class_name": "Course Name",
    "instructor_name": "<EMAIL>"
}

// Calendar Sharing API
POST https://gclassroom-839391304260.us-central1.run.app/share_calender
Headers: x-api-key: G$$gle@VedMG!@#
Body: {
    "calendar_id": "<EMAIL>",
    "user_email": "<EMAIL>",
    "instructor_email": "<EMAIL>"
}

// Calendar Invite API
POST https://gclassroom-839391304260.us-central1.run.app/share_invite
Headers: x-api-key: G$$gle@VedMG!@#
Body: {
    "calendar_id": "<EMAIL>",
    "user_email": "<EMAIL>",
    "summary": "Course Orientation",
    "location": "Online via Google Meet",
    "description": "Welcome session details",
    "start_datetime": "2025-08-22T14:00:00+05:30",
    "end_datetime": "2025-08-22T15:00:00+05:30",
    "instructor_email": "<EMAIL>",
    "meeting_frequency": "FREQ=WEEKLY;BYDAY=WE;COUNT=4"
}
```

#### **Course Listing API**
```php
// List Courses API
POST https://gclassroom-839391304260.us-central1.run.app/list_courses
Headers: x-api-key: G$$gle@VedMG!@#
Body: {
    "instructor_email": "<EMAIL>"
}
```

### **WordPress Integration APIs**

#### **AJAX Endpoints**
- `vedmg_sync_google_classroom`: Synchronize instructor's Google Classrooms
- `vedmg_create_course`: Create new course with Google Classroom integration
- `vedmg_enroll_student`: Manual student enrollment
- `vedmg_sync_masterstudy`: Synchronize with MasterStudy LMS

### **WooCommerce Hooks**
- `woocommerce_order_status_completed`: Trigger automatic enrollment
- `woocommerce_order_status_refunded`: Handle enrollment cancellation
- `woocommerce_product_purchased`: Process course purchase

---

## 🗄️ Database Schema

### **Custom Tables**

#### **1. wp_vedmg_courses**
**Purpose**: Central repository for all courses across platforms
```sql
CREATE TABLE wp_vedmg_courses (
    course_id INT AUTO_INCREMENT PRIMARY KEY,
    masterstudy_course_id INT,
    course_name VARCHAR(255) NOT NULL,
    course_description TEXT,
    instructor_id INT NOT NULL,
    instructor_name VARCHAR(255),
    instructor_email VARCHAR(255),
    google_classroom_id VARCHAR(100),
    calendar_id VARCHAR(255),                    -- NEW: Google Calendar ID
    google_classroom_link VARCHAR(500),
    classroom_status ENUM('active', 'inactive', 'pending'),
    course_status ENUM('published', 'draft', 'archived'),
    woocommerce_product_id INT,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_instructor (instructor_id),
    INDEX idx_google_classroom (google_classroom_id),
    INDEX idx_masterstudy (masterstudy_course_id),
    INDEX idx_calendar (calendar_id)             -- NEW: Calendar ID index
);
```

#### **2. wp_vedmg_student_enrollments**
**Purpose**: Track all student enrollments across platforms
```sql
CREATE TABLE wp_vedmg_student_enrollments (
    enrollment_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    student_name VARCHAR(255),
    student_email VARCHAR(255),
    student_phone VARCHAR(20),
    course_id INT NOT NULL,
    google_classroom_id VARCHAR(100),
    woocommerce_order_id VARCHAR(100),
    enrollment_status ENUM('enrolled', 'completed', 'cancelled', 'suspended'),
    purchase_date DATETIME,
    enrollment_date DATETIME,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_google_classroom (google_classroom_id),
    INDEX idx_enrollment_status (enrollment_status),
    FOREIGN KEY (course_id) REFERENCES wp_vedmg_courses(course_id)
);
```

#### **3. wp_vedmg_instructor_sync**
**Purpose**: Track instructor synchronization status
```sql
CREATE TABLE wp_vedmg_instructor_sync (
    sync_id INT AUTO_INCREMENT PRIMARY KEY,
    wordpress_user_id INT NOT NULL,
    instructor_name VARCHAR(255),
    instructor_email VARCHAR(255),
    instructor_phone VARCHAR(20),
    sync_status ENUM('synced', 'not_synced', 'pending', 'error'),
    google_classroom_count INT DEFAULT 0,
    last_synced_date DATETIME,
    sync_error_message TEXT,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_instructor (wordpress_user_id),
    INDEX idx_sync_status (sync_status)
);
```

---

## 🚀 Installation & Setup

### **Prerequisites**
- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.7 or higher
- WooCommerce plugin (for e-commerce integration)
- MasterStudy LMS plugin (for LMS integration)
- Google Workspace account with Classroom API access

### **Installation Steps**

#### **1. Plugin Installation**
```bash
# Upload plugin files to WordPress
wp-content/plugins/VedMG-ClassRoom/

# Or install via WordPress admin
Plugins → Add New → Upload Plugin → Select VedMG-ClassRoom.zip
```

#### **2. Database Setup**
The plugin automatically creates required tables upon activation:
```php
// Tables created automatically:
- wp_vedmg_courses
- wp_vedmg_student_enrollments
- wp_vedmg_instructor_sync
- wp_vedmg_class_sessions
- wp_vedmg_student_classroom_mappings
```

#### **3. Google API Configuration**
1. **Create Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create new project or select existing
   - Enable Google Classroom API
   - Enable Google Calendar API

2. **Configure OAuth Credentials**
   ```php
   // Add to wp-config.php
   define('VEDMG_GOOGLE_CLIENT_ID', 'your-client-id');
   define('VEDMG_GOOGLE_CLIENT_SECRET', 'your-client-secret');
   define('VEDMG_GOOGLE_REDIRECT_URI', 'https://yoursite.com/google-callback');
   ```

3. **Set API Key**
   ```php
   // Current API configuration
   $api_key = 'G$$gle@VedMG!@#';
   $api_base_url = 'https://gclassroom-839391304260.us-central1.run.app';
   ```

#### **4. Plugin Activation**
```php
// Activate plugin
wp plugin activate vedmg-classroom

// Verify installation
WordPress Admin → VedMG ClassRoom → Dashboard
```

### **Configuration**

#### **1. Instructor Setup**
1. Navigate to **VedMG ClassRoom → Instructors**
2. Add instructor users with Google Workspace emails
3. Click **"Sync with Google Classroom"** for each instructor
4. Complete Google OAuth authentication when prompted

#### **2. WooCommerce Integration**
1. Ensure WooCommerce is installed and activated
2. Create course products in WooCommerce
3. Link products to VedMG courses via course settings
4. Configure automatic enrollment settings

#### **3. Calendar Configuration**
1. Verify Google Calendar API is enabled
2. Test calendar creation with sample course
3. Configure default meeting settings
4. Set up recurring session templates

---

## 📖 Usage Guide

### **👨‍🏫 For Instructors**

#### **Syncing Google Classrooms**
1. **Access Sync Interface**
   ```
   WordPress Admin → VedMG ClassRoom → Instructor Sync
   ```

2. **Perform Sync**
   - Click **"Sync with Google Classroom"** button
   - Complete Google authentication if prompted
   - Monitor sync progress in real-time
   - Review sync results and any errors

3. **Sync Results**
   - **Matched Courses**: Existing courses linked to Google Classrooms
   - **New Courses**: New courses created from Google Classrooms
   - **Student Enrollments**: Students automatically enrolled from Google

#### **Managing Courses**
1. **Course Overview**
   ```
   WordPress Admin → VedMG ClassRoom → Courses
   ```

2. **Course Actions**
   - View course details and enrollment statistics
   - Manage Google Classroom integration
   - Configure calendar settings
   - Monitor student progress

### **🛒 For Store Managers**

#### **WooCommerce Integration**
1. **Product Setup**
   - Create course products in WooCommerce
   - Set product type to "Simple" or "Variable"
   - Configure pricing and descriptions

2. **Course Linking**
   - Edit product in WooCommerce
   - Navigate to **VedMG Course** tab
   - Select corresponding VedMG course
   - Save product settings

3. **Automatic Enrollment**
   - Students automatically enrolled upon purchase completion
   - Calendar access granted immediately
   - Welcome emails sent with course details

#### **Order Management**
1. **Enrollment Tracking**
   ```
   WordPress Admin → VedMG ClassRoom → Enrollments
   ```

2. **Order Integration**
   - View WooCommerce order details
   - Track enrollment status
   - Handle refunds and cancellations

### **👥 For Administrators**

#### **Dashboard Overview**
1. **Main Dashboard**
   ```
   WordPress Admin → VedMG ClassRoom → Dashboard
   ```

2. **Key Metrics**
   - Total courses across all platforms
   - Active student enrollments
   - Sync status summary
   - Recent activity log

#### **Student Management**
1. **Enrollment Management**
   ```
   WordPress Admin → VedMG ClassRoom → Enrollments
   ```

2. **Student Operations**
   - View all student enrollments
   - Manual enrollment/unenrollment
   - Progress tracking
   - Communication tools

#### **System Monitoring**
1. **Sync Status Monitoring**
   - Real-time sync operation status
   - Error tracking and resolution
   - Performance metrics
   - API usage statistics

2. **Debug and Troubleshooting**
   ```
   WordPress Admin → VedMG ClassRoom → Debug Logs
   ```

### **📅 Calendar Features**

#### **Automatic Calendar Sharing**
1. **Course Purchase Flow**
   ```
   Student Purchases Course →
   Enrollment Created →
   Calendar Shared →
   Welcome Invite Sent
   ```

2. **Calendar Access**
   - Students receive calendar access via email
   - Automatic orientation session scheduling
   - Recurring weekly meetings setup
   - Google Meet integration

#### **Meeting Management**
1. **Session Scheduling**
   - Automatic orientation sessions
   - Recurring weekly meetings
   - Custom session creation
   - Calendar event management

2. **Student Notifications**
   - Email invitations with calendar events
   - Meeting reminders
   - Schedule updates
   - Cancellation notifications

---

## 🆕 Recent Updates

### **Version 2.1.0 - August 2025**

#### **🎉 Major New Features**

##### **Advanced Calendar Integration**
- **✅ Automatic Calendar Creation**: New fallback API for calendar creation when primary API doesn't return calendar ID
- **✅ Calendar Sharing System**: Automatic calendar sharing with enrolled students via `share_calender` API
- **✅ Meeting Invitations**: Personalized calendar invites with course orientation sessions
- **✅ Recurring Events**: Support for weekly recurring meetings and sessions

##### **Enhanced Course Creation Flow**
- **✅ Dual API Support**: Primary `create_class` API with fallback `create_class_calendar` API
- **✅ Calendar ID Management**: Automatic detection and storage of calendar IDs
- **✅ Error Recovery**: Robust error handling with automatic fallback mechanisms
- **✅ Real-time Feedback**: Live progress updates during course creation

##### **WooCommerce Calendar Integration**
- **✅ Purchase-Triggered Calendar Sharing**: Automatic calendar access upon course purchase
- **✅ Welcome Session Scheduling**: Automatic orientation session creation for new students
- **✅ Sequential API Calls**: Optimized flow - share calendar first, then send invites
- **✅ Error Handling**: Graceful handling of API failures with detailed logging

#### **🔧 Technical Improvements**

##### **Database Enhancements**
- **✅ Calendar ID Column**: Added `calendar_id` field to `wp_vedmg_courses` table
- **✅ Enhanced Indexing**: Improved database performance with calendar ID indexing
- **✅ Data Integrity**: Better foreign key relationships and constraints

##### **API Integration Improvements**
- **✅ New Calendar APIs**: Integration with `create_class_calendar` and `share_calender` endpoints
- **✅ Enhanced Error Handling**: Comprehensive error tracking and recovery mechanisms
- **✅ Response Validation**: Improved API response parsing and validation
- **✅ Rate Limiting**: Better handling of API rate limits and timeouts

##### **Code Quality & Performance**
- **✅ Modular Architecture**: Improved code organization and separation of concerns
- **✅ Enhanced Logging**: Comprehensive debug logging system for troubleshooting
- **✅ Security Improvements**: Enhanced input validation and sanitization
- **✅ Performance Optimization**: Reduced database queries and improved caching

#### **🐛 Bug Fixes**
- **✅ Fixed**: Calendar ID not being stored when returned from course creation API
- **✅ Fixed**: Student enrollment failing when Google Classroom student data is incomplete
- **✅ Fixed**: Sync status not updating correctly after successful operations
- **✅ Fixed**: WooCommerce integration not triggering calendar sharing
- **✅ Fixed**: Duplicate enrollment prevention in multi-platform scenarios

#### **📊 New Admin Features**
- **✅ Calendar Status Monitoring**: Real-time tracking of calendar creation and sharing
- **✅ Enhanced Course Dashboard**: Improved course management interface with calendar integration
- **✅ API Response Logging**: Detailed logging of all API interactions for debugging
- **✅ Bulk Operations**: Support for bulk calendar sharing and invitation sending

### **Version 2.0.5 - July 2025**
- **✅ Enhanced Google Classroom sync with improved course matching algorithm
- **✅ Added support for MasterStudy LMS integration
- **✅ Improved error handling and user feedback
- **✅ Performance optimizations for large datasets

### **Version 2.0.0 - June 2025**
- **✅ Complete rewrite with modern WordPress standards
- **✅ Introduction of comprehensive database schema
- **✅ Google Classroom API integration
- **✅ WooCommerce integration for automatic enrollment

---

## ⚠️ Known Issues

### **Current Limitations**

#### **Google API Dependencies**
- **Calendar API Reliability**: External calendar APIs occasionally return 500 errors
  - **Impact**: Calendar sharing may fail intermittently
  - **Workaround**: Automatic retry mechanism implemented
  - **Status**: Monitoring API provider for resolution

#### **Sync Performance**
- **Large Classroom Sync**: Syncing instructors with 50+ classrooms may timeout
  - **Impact**: Partial sync results for high-volume instructors
  - **Workaround**: Batch processing implementation in progress
  - **Status**: Planned for version 2.2.0

#### **Platform Compatibility**
- **MasterStudy Versions**: Some older MasterStudy LMS versions may have compatibility issues
  - **Impact**: Limited sync functionality with legacy versions
  - **Workaround**: Manual enrollment options available
  - **Status**: Ongoing compatibility testing

### **Minor Issues**
- **UI Responsiveness**: Admin interface may be slow on mobile devices
- **Email Formatting**: Calendar invitation emails may not display correctly in some email clients
- **Timezone Handling**: Meeting times may not adjust correctly for all timezones

### **Planned Fixes**
- **Version 2.1.1**: Calendar API reliability improvements
- **Version 2.2.0**: Performance optimizations for large datasets
- **Version 2.3.0**: Enhanced mobile interface and email templates

---

## 👨‍💻 Developer Notes

### **Plugin Architecture**

#### **Core Design Patterns**
- **Singleton Pattern**: Core plugin class ensures single instance
- **Factory Pattern**: Database table creation and management
- **Observer Pattern**: Hook-based event system for platform integration
- **Strategy Pattern**: Multiple sync algorithms for different platforms

#### **Code Standards**
- **WordPress Coding Standards**: Full compliance with WordPress PHP coding standards
- **PSR-4 Autoloading**: Modern PHP class autoloading
- **Security First**: All inputs sanitized, outputs escaped, nonces verified
- **Performance Optimized**: Efficient database queries with proper indexing

### **Development Environment Setup**

#### **Local Development**
```bash
# Clone repository
git clone https://github.com/vedmg/classroom-plugin.git

# Install dependencies
composer install
npm install

# Setup WordPress environment
wp core download
wp config create --dbname=vedmg_dev --dbuser=root --dbpass=password

# Activate plugin
wp plugin activate vedmg-classroom
```

#### **Testing Framework**
```php
// PHPUnit tests
composer run test

// JavaScript tests
npm run test

// Integration tests
wp eval-file tests/integration/test-google-sync.php
```

### **Extending the Plugin**

#### **Adding New Integrations**
```php
// Create new integration class
class VedMG_Custom_Integration {
    public function __construct() {
        add_action('vedmg_course_created', [$this, 'handle_course_creation']);
        add_action('vedmg_student_enrolled', [$this, 'handle_enrollment']);
    }

    public function handle_course_creation($course_data) {
        // Custom integration logic
    }
}

// Register integration
add_action('vedmg_init_integrations', function() {
    new VedMG_Custom_Integration();
});
```

#### **Custom API Endpoints**
```php
// Add custom REST API endpoint
add_action('rest_api_init', function() {
    register_rest_route('vedmg/v1', '/custom-endpoint', [
        'methods' => 'POST',
        'callback' => 'vedmg_custom_endpoint_handler',
        'permission_callback' => 'vedmg_check_permissions'
    ]);
});
```

### **Database Customization**

#### **Adding Custom Fields**
```php
// Extend course table
add_action('vedmg_database_upgrade', function($version) {
    if (version_compare($version, '2.2.0', '<')) {
        global $wpdb;
        $wpdb->query("ALTER TABLE {$wpdb->prefix}vedmg_courses ADD COLUMN custom_field VARCHAR(255)");
    }
});
```

#### **Custom Queries**
```php
// Efficient course queries
class VedMG_Course_Query {
    public static function get_courses_by_instructor($instructor_id, $args = []) {
        global $wpdb;

        $defaults = [
            'status' => 'active',
            'limit' => 20,
            'offset' => 0
        ];

        $args = wp_parse_args($args, $defaults);

        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}vedmg_courses
             WHERE instructor_id = %d AND classroom_status = %s
             LIMIT %d OFFSET %d",
            $instructor_id,
            $args['status'],
            $args['limit'],
            $args['offset']
        ));
    }
}
```

### **Security Considerations**

#### **Data Sanitization**
```php
// Always sanitize inputs
$course_name = sanitize_text_field($_POST['course_name']);
$instructor_email = sanitize_email($_POST['instructor_email']);
$course_id = absint($_POST['course_id']);
```

#### **Capability Checks**
```php
// Verify user permissions
if (!current_user_can('manage_vedmg_courses')) {
    wp_die('Insufficient permissions');
}
```

#### **Nonce Verification**
```php
// Verify nonces for all forms
if (!wp_verify_nonce($_POST['vedmg_nonce'], 'vedmg_action')) {
    wp_die('Security check failed');
}
```

### **Performance Optimization**

#### **Caching Strategy**
```php
// Implement object caching
$cache_key = "vedmg_instructor_courses_{$instructor_id}";
$courses = wp_cache_get($cache_key);

if (false === $courses) {
    $courses = VedMG_Course_Query::get_courses_by_instructor($instructor_id);
    wp_cache_set($cache_key, $courses, '', 300); // 5 minutes
}
```

#### **Database Optimization**
```sql
-- Optimize frequent queries
EXPLAIN SELECT * FROM wp_vedmg_courses WHERE instructor_id = 123;

-- Add composite indexes for complex queries
CREATE INDEX idx_course_status_instructor ON wp_vedmg_courses(classroom_status, instructor_id);
```

### **Debugging and Logging**

#### **Debug Mode**
```php
// Enable debug mode
define('VEDMG_DEBUG', true);

// Debug logging
if (defined('VEDMG_DEBUG') && VEDMG_DEBUG) {
    error_log('VedMG Debug: ' . print_r($data, true));
}
```

#### **Custom Logging**
```php
// Use plugin logging system
vedmg_log_info('SYNC', 'Course sync completed', [
    'course_id' => $course_id,
    'students_synced' => $student_count
]);

vedmg_log_error('API', 'Google API call failed', [
    'endpoint' => $api_url,
    'error' => $error_message
]);
```

---

## 📞 Support & Contributing

### **Getting Help**
- **Documentation**: [Plugin Documentation](https://docs.vedmg.com/classroom-plugin)
- **Support Forum**: [WordPress Support](https://wordpress.org/support/plugin/vedmg-classroom)
- **Email Support**: <EMAIL>

### **Contributing**
- **GitHub Repository**: [VedMG ClassRoom Plugin](https://github.com/vedmg/classroom-plugin)
- **Issue Tracking**: Report bugs and feature requests via GitHub Issues
- **Pull Requests**: Contributions welcome following WordPress coding standards

### **License**
This plugin is licensed under the GPL v2 or later.

---

**Last Updated**: August 2025
**Plugin Version**: 2.1.0
**Tested WordPress Version**: 6.3
**Minimum PHP Version**: 7.4

#### **4. wp_vedmg_class_sessions**
**Purpose**: Manage scheduled class sessions and meetings
```sql
CREATE TABLE wp_vedmg_class_sessions (
    session_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    session_title VARCHAR(255),
    session_description TEXT,
    scheduled_date DATE,
    start_time TIME,
    end_time TIME,
    session_status ENUM('scheduled', 'completed', 'cancelled'),
    google_meet_link VARCHAR(500),
    google_calendar_event_id VARCHAR(100),
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_course_date (course_id, scheduled_date),
    FOREIGN KEY (course_id) REFERENCES wp_vedmg_courses(course_id)
);
```

#### **5. wp_vedmg_student_classroom_mappings**
**Purpose**: Bridge table linking WordPress students to Google Classroom
```sql
CREATE TABLE wp_vedmg_student_classroom_mappings (
    mapping_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    student_email VARCHAR(255),
    google_classroom_id VARCHAR(100) NOT NULL,
    google_student_id VARCHAR(100),
    course_id INT NOT NULL,
    enrollment_status ENUM('active', 'removed', 'suspended'),
    sync_status ENUM('synced', 'pending', 'error', 'not_synced'),
    last_sync_date DATETIME,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_student_classroom (student_id, google_classroom_id),
    INDEX idx_google_classroom (google_classroom_id),
    INDEX idx_sync_status (sync_status)
);
```
